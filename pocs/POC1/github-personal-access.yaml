id: 384e1fb9-97e7-471f-8407-ef3f9caa1bd2
info:
  name: GitHub Personal Access Token
  author: DhiyaneshDK
  severity: high
  reference:
  - https://github.com/praetorian-inc/noseyparker/blob/main/crates/noseyparker/data/default/builtin/rules/github.yml
  - https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/about-authentication-to-github
  - https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/creating-a-personal-access-token
  - https://github.blog/2021-04-05-behind-githubs-new-authentication-token-formats/
  metadata:
    verified: true
    max-request: 1
  tags: github,token,exposure
  poc_id: 384e1fb9-97e7-471f-8407-ef3f9caa1bd2
  old_id: github-personal-access
flow: http(1) && http(2)
http:
- method: GET
  path:
  - '{{BaseURL}}'
  extractors:
  - type: regex
    part: body
    name: token
    regex:
    - \b(ghp_[a-zA-Z0-9]{36})\b
    internal: true
- raw:
  - '@Host: https://api.github.com:443

    GET /user HTTP/1.1

    Host: api.github.com

    Authorization: Basic {{base64(''user:'' + token)}}

    '
  self-contained: true
  matchers:
  - type: word
    part: body
    words:
    - '"login":'
    - '"avatar_url":'
    condition: and
