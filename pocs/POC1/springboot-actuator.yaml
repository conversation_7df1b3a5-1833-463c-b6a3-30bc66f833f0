id: 247abdaf-f80f-4319-ab85-92838df404e5
info:
  name: Detect Springboot Actuators
  author: that_juan_,dwisiswant0,wdahlenb,dr0pd34d
  severity: medium
  metadata:
    shodan-query: http.favicon.hash:116323821
  tags: tech,springboot,actuator
  poc_id: 247abdaf-f80f-4319-ab85-92838df404e5
  old_id: springboot-actuator
requests:
- method: GET
  path:
  - '{{BaseURL}}'
  - '{{BaseURL}}/actuator'
  - '{{BaseURL}}/favicon.ico'
  - '{{BaseURL}}/actuator/favicon.ico'
  stop-at-first-match: true
  matchers-condition: or
  matchers:
  - type: word
    part: body
    words:
    - '"_links":'
    - '"self":'
    - '"health"'
    condition: and
  - type: dsl
    name: favicon
    dsl:
    - status_code==200 && ("116323821" == mmh3(base64_py(body)))
  extractors:
  - type: json
    name: available-endpoints
    json:
    - .[] | to_entries | .[].key
