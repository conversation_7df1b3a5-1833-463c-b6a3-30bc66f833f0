id: a760bb81-f821-4196-8c31-a4c68dcc72db
info:
  name: ASP-Nuke - Open Redirect
  author: pdteam
  severity: low
  description: ASP-Nuke contains an open redirect vulnerability. An attacker can redirect
    a user to a malicious site and possibly obtain sensitive information, modify data,
    and/or execute unauthorized operations.
  reference:
  - https://packetstormsecurity.com/files/125931/ASP-Nuke-2.0.7-Open-Redirect.html
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
    cvss-score: 6.1
    cwe-id: CWE-601
  tags: packetstorm,aspnuke,redirect
  poc_id: a760bb81-f821-4196-8c31-a4c68dcc72db
  old_id: aspnuke-openredirect
requests:
- method: GET
  path:
  - '{{BaseURL}}/gotoURL.asp?url=interact.sh&id=43569'
  matchers:
  - type: regex
    part: header
    regex:
    - (?m)^(?:Location\s*:\s*)(?:https?://|//)?(?:[a-zA-Z0-9\-_]*\.)?interact\.sh(?:\s*)$
