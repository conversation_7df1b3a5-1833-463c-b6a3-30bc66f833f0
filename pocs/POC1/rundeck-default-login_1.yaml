id: d22d9314-7ca1-4026-9184-16d29ecb9328
info:
  name: Rundeck Default Login
  author: kaks3c
  severity: critical
  description: <PERSON><PERSON> using rundeck default admin credentials.
  reference:
  - https://github.com/karkis3c/bugbounty/blob/main/poc/rundeck-rce.md
  poc_id: d22d9314-7ca1-4026-9184-16d29ecb9328
  old_id: rundeck-deault-login
http:
- method: POST
  path:
  - http://{{Hostname}}/j_security_check
  headers:
    Content-Type: application/x-www-form-urlencoded
  body: j_username={{username}}&j_password={{password}}
  payloads:
    username:
    - admin
    password:
    - admin
  attack: pitchfork
  matchers:
  - type: word
    part: header
    words:
    - /user/error
    - grails_remember_me=
    condition: and
    negative: true
