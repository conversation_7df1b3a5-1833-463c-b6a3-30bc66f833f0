id: a97e1c14-4ab1-4167-8517-f22b0c54b2d0
info:
  name: Exposed Browserless debugger
  author: ggranjus
  severity: medium
  description: Browserless instance can be used to make web requests. May worth checking
    /workspace for juicy files.
  reference: https://docs.browserless.io/docs/docker.html#securing-your-instance
  metadata:
    shodan-query: http.title:"browserless debugger"
  tags: browserless,unauth,debug
  poc_id: a97e1c14-4ab1-4167-8517-f22b0c54b2d0
  old_id: browserless-debugger
http:
- method: GET
  path:
  - '{{BaseURL}}'
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - <title>browserless debugger</title>
    - <code>Click the ► button to run your code.</code>
    condition: or
  - type: status
    status:
    - 200
