id: 02f34177-b7cd-4ad8-baf6-810723ed5c64
info:
  name: elFinder 2.1.58 - Remote Code Execution
  author: idealphase
  severity: critical
  description: elFinder 2.1.58 is vulnerable to remote code execution. This can allow
    an attacker to execute arbitrary code and commands on the server hosting the elFinder
    PHP connector, even with minimal configuration.
  remediation: The issues were patched in version 2.1.59. As a workaround, ensure
    the connector is not exposed without authentication.
  reference:
  - https://github.com/Studio-42/elFinder/
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H
    cvss-score: 10.0
    cwe-id: CWE-77
  tags: tech,elfinder,oss
  poc_id: 02f34177-b7cd-4ad8-baf6-810723ed5c64
  old_id: elfinder-version
requests:
- method: GET
  path:
  - '{{BaseURL}}/js/elfinder.min.js'
  - '{{BaseURL}}/js/elFinder.version.js'
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - elFinder - file manager for web
    - elFinder.prototype.version =
    condition: or
  - type: status
    status:
    - 200
  extractors:
  - type: regex
    group: 1
    regex:
    - \* Version (.+) \(
    - elFinder.prototype.version = '([0-9.]+)';
