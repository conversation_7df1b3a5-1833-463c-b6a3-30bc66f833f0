id: d7b6e149-13b1-42bc-a731-f6608d24a2e8
info:
  name: phpinfo Resource Exposure
  author: whoever
  severity: medium
  description: phpinfo() is susceptible to resource exposure in unprotected composer
    vendor folders via phpfastcache/phpfastcache.
  reference: https://github.com/PHPSocialNetwork/phpfastcache/pull/813 https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-37704
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:N/A:N
    cvss-score: 4.3
    cve-id: CVE-2021-37704
    cwe-id: CWE-668
  tags: cve,cve2021,exposure,phpfastcache,phpinfo
  poc_id: d7b6e149-13b1-42bc-a731-f6608d24a2e8
  old_id: CVE-2021-37704
requests:
- method: GET
  path:
  - '{{BaseURL}}/vendor/phpfastcache/phpfastcache/docs/examples/phpinfo.php'
  - '{{BaseURL}}/vendor/phpfastcache/phpfastcache/examples/phpinfo.php'
  matchers-condition: and
  matchers:
  - type: word
    words:
    - PHP Extension
    - PHP Version
    condition: and
  - type: status
    status:
    - 200
  extractors:
  - type: regex
    part: body
    group: 1
    regex:
    - '>PHP Version <\/td><td class="v">([0-9.]+)'
