id: ae35f5d3-8015-4f3d-bdcb-7ed0cb0d4515
info:
  name: X Prober server information leakage
  author: pdteam
  severity: low
  reference:
  - https://twitter.com/bugbounty_tips/status/1339984643517423616
  tags: config,exposure
  poc_id: ae35f5d3-8015-4f3d-bdcb-7ed0cb0d4515
  old_id: xprober-service
requests:
- method: GET
  path:
  - '{{BaseURL}}/xprober.php'
  matchers:
  - type: word
    words:
    - '"appName":"X Prober"'
    - <title>X Prober
    condition: and
