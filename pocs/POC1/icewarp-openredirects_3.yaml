id: fe04b9a5-c469-4b7f-947e-6fb5f85620cc
info:
  name: IceWarp - Open Redirect
  author: uomogrande
  severity: medium
  description: Detects icewarp open redirects / fixed in Version ********
  metadata:
    verified: true
    shodan-query: title:"icewarp"
  tags: icewarp,redirect
  poc_id: fe04b9a5-c469-4b7f-947e-6fb5f85620cc
  old_id: icewarp-open-redirect
requests:
- raw:
  - 'GET ///interact.sh/%2F.. HTTP/1.1

    '
  matchers-condition: and
  matchers:
  - type: word
    part: header
    words:
    - IceWarp
  - type: regex
    part: header
    regex:
    - (?m)^(?:Location\s*?:\s*?)(?:https?:\/\/|\/\/|\/\\\\|\/\\)?(?:[a-zA-Z0-9\-_\.@]*)interact\.sh\/?(\/|[^.].*)?$
  extractors:
  - type: regex
    name: redirected
    part: header
    group: 1
    regex:
    - 'Server: (.{4,20})'
