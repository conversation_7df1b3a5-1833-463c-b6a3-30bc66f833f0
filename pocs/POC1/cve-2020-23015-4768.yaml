id: a0c77216-4444-49bd-b4dc-c98f57e8dc2a
info:
  name: OPNsense 20.1.5. Open Redirect
  author: 0x_Akoko
  severity: medium
  description: An open redirect issue was discovered in OPNsense through 20.1.5. The
    redirect parameter "url" in login page was not filtered and can redirect user
    to any website.
  reference:
  - https://github.com/opnsense/core/issues/4061
  - https://www.cvedetails.com/cve/CVE-2020-23015
  tags: cve,cve2020,redirect,opnsense
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
    cvss-score: 6.1
    cve-id: CVE-2020-23015
    cwe-id: CWE-601
  poc_id: a0c77216-4444-49bd-b4dc-c98f57e8dc2a
  old_id: CVE-2020-23015
requests:
- method: GET
  path:
  - '{{BaseURL}}/?url=http://example.com'
  matchers:
  - type: regex
    regex:
    - (?m)^(?:Location\s*?:\s*?)(?:https?://|//)?(?:[a-zA-Z0-9\-_]*\.)?example\.com(?:\s*?)$
    part: header
