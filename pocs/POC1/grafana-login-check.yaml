id: 0126f3fc-cd4a-403d-ac1f-8103b116ce12
info:
  name: <PERSON><PERSON>gin Check
  author: parthmalhotra,pdresearch
  severity: critical
  description: Checks for a valid login on self hosted Grafana instance.
  reference:
  - https://owasp.org/www-community/attacks/Credential_stuffing
  metadata:
    max-request: 1
    shodan-query: title:"Grafana"
    fofa-query: title="Grafana"
  tags: self-hosted,creds-stuffing,login-check,grafana
  poc_id: 0126f3fc-cd4a-403d-ac1f-8103b116ce12
  old_id: grafana-login-check
variables:
  username: '{{username}}'
  password: '{{password}}'
http:
- raw:
  - 'POST /login HTTP/1.1

    Host: {{Hostname}}

    accept: application/json, text/plain, */*

    DNT: 1

    content-type: application/json

    Origin: {{BaseURL}}

    Referer: {{BaseURL}}/login

    Cookie: redirect_to=%2F


    {"user":"{{username}}","password":"{{password}}"}

    '
  extractors:
  - type: dsl
    dsl:
    - username
    - password
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - Logged in
  - type: word
    part: header
    words:
    - grafana_session
  - type: status
    status:
    - 200
