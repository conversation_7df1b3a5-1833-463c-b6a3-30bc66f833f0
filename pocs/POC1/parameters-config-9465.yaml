id: 33905a21-dbcc-47bf-a1a7-520cb6a5a971
info:
  name: Parameters.yml - File Discovery
  author: DhiyaneshDK
  severity: high
  description: Parameters.yml was discovered.
  reference: https://www.exploit-db.com/ghdb/5986
  metadata:
    verified: true
    shodan-query: html:"parameters.yml"
  tags: exposure,cloud,devops
  poc_id: 33905a21-dbcc-47bf-a1a7-520cb6a5a971
  old_id: parameters-config
requests:
- method: GET
  path:
  - '{{BaseURL}}/parameters.yml'
  - '{{BaseURL}}/app/config/parameters.yml'
  - '{{BaseURL}}/parameters.yml.dist'
  - '{{BaseURL}}/app/config/parameters.yml.dist'
  stop-at-first-match: true
  matchers-condition: and
  matchers:
  - type: word
    words:
    - 'parameters:'
    - database_user
    - database_password
    condition: and
  - type: status
    status:
    - 200
