id: 8a9f8f40-9d94-45b1-aaa1-4d4070ae9b22
info:
  name: Basic Host Header Poisoning
  author: tedm.infosec
  severity: medium
  description: Basic host header poisoning vulnerability check. Checks for reflections
    of X-Forwarded-Host and X-Host in the response body.
  poc_id: 8a9f8f40-9d94-45b1-aaa1-4d4070ae9b22
  old_id: host-header-poisoning
requests:
- raw:
  - 'GET /?slash-host HTTP/1.1

    Host: xrcr0x4a.com/{{Hostname}}

    X-Forwarded-Host: xrcr0x4a.com

    '
  - 'GET /?x-forwarded-host HTTP/1.1

    Host: {{Hostname}}

    X-Forwarded-Host: xrcr0x4a.com

    '
  - 'GET /?x-host HTTP/1.1

    Host: {{Hostname}}

    X-Host: xrcr0x4a.com

    '
  - 'GET /?x-forwarded-server HTTP/1.1

    Host: {{Hostname}}

    X-Forwarded-Server: xrcr0x4a.com

    '
  - 'GET /?x-http-host-override HTTP/1.1

    Host: {{Hostname}}

    X-HTTP-Host-Override: xrcr0x4a.com

    '
  - 'GET /?forwarded HTTP/1.1

    Host: {{Hostname}}

    Forwarded: xrcr0x4a.com

    '
  - 'GET /?non-numeric-port-bypass HTTP/1.1

    Host: {{Hostname}}:xrcr0x4a

    '
  - 'GET /?subdomain-bypass HTTP/1.1

    Host: xrcr0x4a.{{Hostname}}

    '
  - 'GET /?duplicate-host-headers HTTP/1.1

    Host: {{Hostname}}

    Host: xrcr0x4a.com

    '
  - "GET /?duplicate-host-headers-line-wrap HTTP/1.1\n Host: xrcr0x4a.com\nHost: {{Hostname}}\n"
  - 'GET https://{{Hostname}}/?absolute-url HTTP/1.1

    Host: xrcr0x4a.com

    '
  matchers-condition: and
  matchers:
  - type: word
    words:
    - xrcr0x4a
    part: body
    condition: and
  - type: status
    status:
    - 200
