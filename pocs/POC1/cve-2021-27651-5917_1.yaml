id: 1ff892b3-a105-4824-b329-3897a86c6f24
info:
  name: Pega Infinity - Authentication Bypass
  author: idealphase,daffainfo
  severity: critical
  description: Pega Infinity versions 8.2.1 through 8.5.2 contain an authentication
    bypass vulnerability because the password reset functionality for local accounts
    can be used to bypass local authentication checks.
  reference:
  - https://github.com/samwcyo/CVE-2021-27651-PoC/blob/main/RCE.md
  - https://nvd.nist.gov/vuln/detail/CVE-2021-27651
  - https://collaborate.pega.com/discussion/pega-security-advisory-a21-hotfix-matrix
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2021-27651
    cwe-id: CWE-287,CWE-640
  tags: cve,cve2021,pega,auth-bypass
  poc_id: 1ff892b3-a105-4824-b329-3897a86c6f24
  old_id: CVE-2021-27651
requests:
- method: GET
  path:
  - '{{BaseURL}}/prweb/PRAuth/app/default/'
  cookie-reuse: true
  redirects: true
  max-redirects: 2
  extractors:
  - type: regex
    name: version
    internal: true
    group: 1
    regex:
    - (?m)<span>Pega ([0-9.]+)</span>
  - type: regex
    group: 1
    regex:
    - (?m)<span>Pega ([0-9.]+)</span>
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 200
  - type: word
    part: body
    words:
    - Pega Infinity
  - type: dsl
    dsl:
    - compare_versions(version, '< 8.5.2', '>= 8.2.1')
