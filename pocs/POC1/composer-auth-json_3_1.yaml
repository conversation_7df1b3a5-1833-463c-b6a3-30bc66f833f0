id: b8789683-8926-48a9-bbcb-67f12c3504b4
info:
  name: Composer-auth Json File Disclosure
  author: DhiyaneshDK
  severity: low
  reference: https://www.exploit-db.com/ghdb/5768
  metadata:
    verified: true
    google-query: intext:"index of /" ".composer-auth.json"
  tags: exposure,devops,files
  poc_id: b8789683-8926-48a9-bbcb-67f12c3504b4
  old_id: composer-auth-json
http:
- method: GET
  path:
  - '{{BaseURL}}/.composer-auth.json'
  - '{{BaseURL}}/vendor/webmozart/assert/.composer-auth.json'
  stop-at-first-match: true
  matchers-condition: and
  matchers:
  - type: word
    words:
    - github-oauth
    - github.com
    condition: and
  - type: status
    status:
    - 200
