id: cd02b3b5-16c6-4701-be38-363b5c3d42ee
info:
  name: Magento Cacheleak
  author: TechbrunchFR
  severity: high
  description: Magento Cacheleak is an implementation vulnerability, result of bad
    implementation of web-server configuration for Magento platform. Magento was developed
    to work under the Apache web-server which natively works with .htaccess files,
    so all needed configuration directives specific for various internal Magento folders
    were placed in .htaccess files.  When Magento is installed on web servers that
    are ignoring .htaccess files (such as nginx), an attacker can get access to internal
    Magento folders (such as the Magento cache directory) and extract sensitive information
    from cache files.
  reference:
  - https://support.hypernode.com/en/best-practices/security/how-to-secure-magento-cacheleak
  - https://www.acunetix.com/vulnerabilities/web/magento-cacheleak/
  - https://royduineveld.nl/magento-cacheleak-exploit/
  tags: magento
  poc_id: cd02b3b5-16c6-4701-be38-363b5c3d42ee
  old_id: magento-cacheleak
http:
- method: GET
  path:
  - '{{BaseURL}}/var/resource_config.json'
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 200
  - type: word
    words:
    - media_directory
    - allowed_resources
    part: body
  - type: word
    words:
    - application/json
    part: header
