id: 9842dfdb-3beb-410d-a52b-210672794db1
info:
  name: Detect Private SSL, SSH, TLS, and JWT Keys
  author: geeknik,R12W4N
  severity: high
  tags: config,exposure
  poc_id: 9842dfdb-3beb-410d-a52b-210672794db1
  old_id: server-private-keys
requests:
- method: GET
  path:
  - '{{BaseURL}}/localhost.key'
  - '{{BaseURL}}/host.key'
  - '{{BaseURL}}/www.key'
  - '{{BaseURL}}/private-key'
  - '{{BaseURL}}/privatekey.key'
  - '{{BaseURL}}/server.key'
  - '{{BaseURL}}/my.key'
  - '{{BaseURL}}/key.pem'
  - '{{BaseURL}}/ssl/localhost.key'
  - '{{BaseURL}}/ssl/{{Hostname}}.key'
  - '{{BaseURL}}/id_rsa'
  - '{{BaseURL}}/id_dsa'
  - '{{BaseURL}}/.ssh/id_rsa'
  - '{{BaseURL}}/.ssh/id_dsa'
  - '{{BaseURL}}/{{Hostname}}.key'
  - '{{BaseURL}}/{{Hostname}}.pem'
  - '{{BaseURL}}/config/jwt/private.pem'
  - '{{BaseURL}}/jwt/private.pem'
  - '{{BaseURL}}/var/jwt/private.pem'
  - '{{BaseURL}}/private.pem'
  - '{{BaseURL}}/ssl.txt'
  - '{{BaseURL}}/ssl_key.txt'
  - '{{BaseURL}}/certificates/{{Host}}.pfx'
  - '{{BaseURL}}/certificates/{{Host}}.p12'
  - '{{BaseURL}}/ssl/{{Host}}.pem'
  - '{{BaseURL}}/ssl/{{Host}}_key.txt'
  - '{{BaseURL}}/cert/{{Host}}_key.txt'
  - '{{BaseURL}}/cert/{{RDN}}_key.txt'
  - '{{BaseURL}}/cert/{{Host}}.txt'
  - '{{BaseURL}}/ssl/private/{{Host}}_key.pem'
  - '{{BaseURL}}/certs/{{Host}}_private.key'
  - '{{BaseURL}}/certs/{{Host}}.key'
  - '{{BaseURL}}/certificates/{{Host}}_priv.pem'
  - '{{BaseURL}}/certificates/{{Host}}_privkey.pem'
  - '{{BaseURL}}/certs/{{Host}}.pem'
  - '{{BaseURL}}/private/{{Host}}.key'
  - '{{BaseURL}}/keys/{{Host}}.pem'
  stop-at-first-match: true
  matchers-condition: and
  matchers:
  - type: word
    words:
    - BEGIN OPENSSH PRIVATE KEY
    - BEGIN PRIVATE KEY
    - BEGIN RSA PRIVATE KEY
    - BEGIN DSA PRIVATE KEY
    - BEGIN EC PRIVATE KEY
    - BEGIN PGP PRIVATE KEY BLOCK
    - BEGIN ENCRYPTED PRIVATE KEY
    condition: or
  - type: status
    status:
    - 200
  - type: dsl
    dsl:
    - '!contains(body_2, "<html")'
    - '!contains(body_2, "<HTML")'
    condition: and
