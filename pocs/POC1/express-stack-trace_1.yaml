id: e39667a9-be6f-4b6e-af8c-cba5a9bc1365
info:
  name: Express Stack Trace
  author: DhiyaneshDk
  severity: low
  description: Express Stack trace is exposed.
  metadata:
    verified: true
    max-request: 1
    shodan-query: html:"Welcome to Express"
  tags: misconfig,express,intrusive
  poc_id: e39667a9-be6f-4b6e-af8c-cba5a9bc1365
  old_id: express-stack-trace
http:
- method: GET
  path:
  - '{{BaseURL}}/{{randstr}}'
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - 'NotFoundError: Not Found'
    - at Function.handle
    condition: and
  - type: word
    part: header
    words:
    - text/html
  - type: status
    status:
    - 404
