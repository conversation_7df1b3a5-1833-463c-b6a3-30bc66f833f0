id: 748ab355-ca5f-4ae4-a366-2235798b5649
info:
  name: Google Service Json
  author: DhiyaneshDK
  severity: low
  reference: https://www.exploit-db.com/ghdb/6886
  metadata:
    verified: true
    google-query: intitle:"index of" "google-services.json"
  tags: google,cloud,exposure,files
  poc_id: 748ab355-ca5f-4ae4-a366-2235798b5649
  old_id: google-services-json
http:
- method: GET
  path:
  - '{{BaseURL}}/google-services.json'
  - '{{BaseURL}}/app/google-services.json'
  - '{{BaseURL}}/android/app/google-services.json'
  stop-at-first-match: true
  matchers-condition: and
  matchers:
  - type: word
    words:
    - storage_bucket
    - oauth_client
    condition: and
  - type: status
    status:
    - 200
