id: 6e1728e1-f0d9-452b-9129-0c482f9cdf78
info:
  name: Detect Private SSH, TLS, and JWT Keys
  author: geeknik
  severity: high
  tags: config,exposure
  poc_id: 6e1728e1-f0d9-452b-9129-0c482f9cdf78
  old_id: server-private-keys8
requests:
- raw:
  - 'GET /key.pem HTTP/1.1

    Host: {{Hostname}}

    User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:55.0) Gecko/20100101
    Firefox/55

    '
  matchers-condition: and
  matchers:
  - type: word
    words:
    - BEGIN OPENSSH PRIVATE KEY
    - BEGIN PRIVATE KEY
    - BEGIN RSA PRIVATE KEY
    - BEGIN DSA PRIVATE KEY
    - BEGIN EC PRIVATE KEY
    - BEGIN PGP PRIVATE KEY BLOCK
    condition: or
  - type: status
    status:
    - 200
