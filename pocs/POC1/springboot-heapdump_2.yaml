id: 0b5487df-f286-4010-8084-97a50041ab65
info:
  name: Spring Boot Actuator - Heap Dump Detection
  author: that_juan_,dwisiswant0,wdahlenb
  severity: critical
  description: 'A Spring Boot Actuator heap dump was detected. A heap dump is a snapshot
    of JVM memory, which could expose environment variables and HTTP requests.

    '
  reference:
  - https://github.com/pyn3rd/Spring-Boot-Vulnerability
  metadata:
    max-request: 3
  tags: springboot,exposure
  poc_id: 0b5487df-f286-4010-8084-97a50041ab65
  old_id: springboot-heapdump
variables:
  str: '{{rand_base(6)}}'
http:
- raw:
  - 'GET /{{str}} HTTP/1.1

    Host: {{Hostname}}

    '
  - 'GET /heapdump HTTP/1.1

    Host: {{Hostname}}

    '
  - 'GET /actuator/heapdump HTTP/1.1

    Host: {{Hostname}}

    '
  max-size: 2097152
  matchers-condition: or
  matchers:
  - type: dsl
    dsl:
    - '!contains(hex_encode(body_1), ''1f8b080000000000'')'
    - contains(hex_encode(body_2), '1f8b080000000000')
    condition: and
  - type: dsl
    dsl:
    - '!contains(hex_encode(body_1), ''1f8b080000000000'')'
    - contains(hex_encode(body_3), '1f8b080000000000')
    condition: and
  - type: dsl
    dsl:
    - contains(hex_encode(body_2), '4a4156412050524f46494c45') || contains(hex_encode(body_2),
      '4850524f46')
    - contains(hex_encode(body_3), '4a4156412050524f46494c45') || contains(hex_encode(body_3),
      '4850524f46')
    condition: or
