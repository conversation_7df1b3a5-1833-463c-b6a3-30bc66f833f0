id: 5198f1b9-0e1d-4e86-bef5-b27487508dcd
info:
  name: Camaleon CMS - Default Login
  author: DhiyaneshDK
  severity: high
  description: 'Camaleon CMS default login credentials was discovered.

    '
  metadata:
    vendor: tuzitio
    product: camaleon_cms
    shodan-query: html:"camaleon_cms"
  tags: camaleon,default-login
  poc_id: 5198f1b9-0e1d-4e86-bef5-b27487508dcd
  old_id: camaleon-default-login
variables:
  username: admin
  password: admin123
flow: http(1) && http(2)
http:
- raw:
  - 'GET /admin/login HTTP/1.1

    Host: {{Hostname}}

    '
  extractors:
  - type: regex
    part: body
    internal: true
    name: nonce
    group: 1
    regex:
    - name="authenticity_token" value="(.*?)"
- raw:
  - 'POST /admin/login HTTP/1.1

    Host: {{Hostname}}

    Content-Type: application/x-www-form-urlencoded


    authenticity_token={{nonce}}&user%5Busername%5D={{username}}&user%5Bpassword%5D={{password}}

    '
  matchers-condition: and
  matchers:
  - type: dsl
    dsl:
    - contains(location,"/admin/dashboard")
