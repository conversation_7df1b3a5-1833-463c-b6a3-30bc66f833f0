id: adf12776-084a-48de-a471-a5f5eb95a013
info:
  name: <PERSON><PERSON><PERSON> Authentication Bypass
  author: CasperGN
  severity: critical
  description: <PERSON><PERSON><PERSON> prior to version 8.1.0 is prone to Authentication Bypass when
    using LDAP as authorization provider and the LDAP server accepts Unauthenticated
    Bind requests.
  reference:
  - https://github.com/advisories/GHSA-5hmm-x8q8-w5jh
  - https://tools.ietf.org/html/rfc4513#section-5.1.2
  - https://pypi.org/project/alerta-server/8.1.0/
  - https://nvd.nist.gov/vuln/detail/CVE-2020-26214
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2020-26214
    cwe-id: CWE-287
  tags: cve,cve2020,alerta,auth-bypass
  poc_id: adf12776-084a-48de-a471-a5f5eb95a013
  old_id: CVE-2020-26214
requests:
- method: GET
  path:
  - '{{BaseURL}}/api/config'
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 200
  - type: regex
    regex:
    - name":\s*"Alerta ([0-7]\.[0-9]\.[0-9]|8\.0.[0-9])"
    condition: or
  - type: regex
    regex:
    - provider":\s*"ldap"
    condition: or
  extractors:
  - type: regex
    part: body
    name: alerta-version
    group: 1
    regex:
    - name":\s*"Alerta ([0-7]\.[0-9]\.[0-9]|8\.0.[0-9])"
