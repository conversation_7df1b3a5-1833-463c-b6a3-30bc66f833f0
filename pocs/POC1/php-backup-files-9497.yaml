id: 5dfdb3d8-d26f-4484-a7af-0505d91dccda
info:
  name: PHP source disclosure through backup files
  author: StreetOfHackerR007 (Roh<PERSON> Soni)
  severity: medium
  tags: exposure,backup,php
  poc_id: 5dfdb3d8-d26f-4484-a7af-0505d91dccda
  old_id: php-backup-files
requests:
- method: GET
  path:
  - '{{BaseURL}}/index.php.bak'
  - '{{BaseURL}}/default.php.bak'
  - '{{BaseURL}}/main.php.bak'
  - '{{BaseURL}}/config.php.bak'
  - '{{BaseURL}}/settings.php.bak'
  - '{{BaseURL}}/header.php.bak'
  - '{{BaseURL}}/footer.php.bak'
  - '{{BaseURL}}/login.php.bak'
  - '{{BaseURL}}/database.php.bak'
  - '{{BaseURL}}/db.php.bak'
  - '{{BaseURL}}/conn.php.bak'
  - '{{BaseURL}}/db_config.php.bak'
  - '{{BaseURL}}/404.php.bak'
  - '{{BaseURL}}/wp-config.php.bak'
  - '{{BaseURL}}/wp-login.php.bak'
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 200
  - type: word
    words:
    - <?php
    - <?=
    condition: or
    part: body
  - type: word
    words:
    - ?>
    part: body
  - type: word
    words:
    - text/plain
    - bytes
    part: header
    condition: or
