id: 0fdf15e5-cd8d-4890-885e-afa5d6676674
info:
  name: Open Redirect - Detection
  author: a<PERSON><PERSON>,me<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,pxmme1337,<PERSON><PERSON>_,andir<PERSON>mani1,geeknik
  severity: medium
  description: An open redirect vulnerability was detected. An attacker can redirect
    a user to a malicious site and possibly obtain sensitive information, modify data,
    and/or execute unauthorized operations.
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
    cvss-score: 6.1
    cwe-id: CWE-601
  metadata:
    max-request: 93
  tags: redirect,generic
  poc_id: 0fdf15e5-cd8d-4890-885e-afa5d6676674
  old_id: open-redirect-generic
http:
- method: GET
  path:
  - '{{RootURL}}/{{redirect}}'
  payloads:
    redirect:
    - '%0a/evil.com/'
    - '%0d/evil.com/'
    - '%00/evil.com/'
    - '%09/evil.com/'
    - '%5C%5Cevil.com/%252e%252e%252f'
    - '%5Cevil.com'
    - '%5cevil.com/%2f%2e%2e'
    - '%5c{{RootURL}}evil.com/%2f%2e%2e'
    - ../evil.com
    - .evil.com
    - /%5cevil.com
    - ////\;@evil.com
    - ////evil.com
    - ///evil.com
    - ///evil.com/%2f%2e%2e
    - ///evil.com@//
    - ///{{RootURL}}evil.com/%2f%2e%2e
    - //;@evil.com
    - //\/evil.com/
    - //\@evil.com
    - //\evil.com
    - //\tevil.com/
    - //evil.com/%2F..
    - //evil.com//
    - //%69%6e%74%65%72%61%63%74%2e%73%68
    - //evil.com@//
    - //evil.com\tevil.com/
    - //https://evil.com@//
    - /<>//evil.com
    - /\/\/evil.com/
    - /\/evil.com
    - /\evil.com
    - /evil.com
    - /evil.com/%2F..
    - /evil.com/
    - /evil.com/..;/css
    - /https:evil.com
    - /{{RootURL}}evil.com/
    - /〱evil.com
    - /〵evil.com
    - /ゝevil.com
    - /ーevil.com
    - /ｰevil.com
    - <>//evil.com
    - '@evil.com'
    - '@https://evil.com'
    - \/\/evil.com/
    - evil%E3%80%82com
    - evil.com
    - evil.com/
    - evil.com//
    - evil.com;@
    - https%3a%2f%2fevil.com%2f
    - https:%0a%0devil.com
    - https://%0a%0devil.com
    - https://%09/evil.com
    - https://%2f%2f.evil.com/
    - https://%3F.evil.com/
    - https://%5c%5c.evil.com/
    - https://%5cevil.com@
    - https://%23.evil.com/
    - https://.evil.com
    - https://////evil.com
    - https:///evil.com
    - https:///evil.com/%2e%2e
    - https:///evil.com/%2f%2e%2e
    - https:///<EMAIL>/%2e%2e
    - https:///<EMAIL>/%2f%2e%2e
    - https://:80#@evil.com/
    - https://:80?@evil.com/
    - https://:@\@evil.com
    - https://:@evil.com\@evil.com
    - https://;@evil.com
    - https://\tevil.com/
    - https://evil.com/evil.com
    - https://evil.com/https://evil.com/
    - https://www.\.evil.com
    - https:/\/\evil.com
    - https:/\evil.com
    - https:/evil.com
    - https:evil.com
    - '{{RootURL}}evil.com'
    - 〱evil.com
    - 〵evil.com
    - ゝevil.com
    - ーevil.com
    - ｰevil.com
    - redirect/evil.com
    - cgi-bin/redirect.cgi?evil.com
    - out?evil.com
    - login?to=http://evil.com
    - 1/<EMAIL>
    - redirect?targeturl=https://evil.com
  stop-at-first-match: true
  matchers-condition: and
  matchers:
  - type: regex
    part: header
    regex:
    - (?m)^(?:Location\s*?:\s*?)(?:https?:\/\/|\/\/|\/\\\\|\/\\)(?:[a-zA-Z0-9\-_\.@]*)evil\.com\/?(\/|[^.].*)?$
  - type: status
    status:
    - 301
    - 302
    - 307
    - 308
    condition: or
