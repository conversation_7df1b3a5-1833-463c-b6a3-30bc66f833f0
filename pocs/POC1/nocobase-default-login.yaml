id: 3427321d-8542-47db-b0c1-3fa11696c1e0
info:
  name: NocoBase - Default Login
  author: Fur1na
  severity: high
  description: 'NocoBase default login was discovered.

    '
  reference:
  - https://www.nocobase.com/
  - https://github.com/nocobase/nocobase
  - https://docs.nocobase.com/welcome/getting-started/installation/docker-compose
  metadata:
    verified: true
    max-request: 1
    zoomeye-query: app="NocoBase"
  tags: default-login,nocobase
  poc_id: 3427321d-8542-47db-b0c1-3fa11696c1e0
  old_id: nocobase-default-login
variables:
  username: <EMAIL>
  password: admin123
http:
- raw:
  - 'POST /api/auth:signIn HTTP/1.1

    Host: {{Hostname}}

    Content-Type: application/json


    {"account": "{{username}}", "password": "{{password}}"}

    '
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - '"username":'
    - '"email":'
    - systemSettings
    condition: and
  - type: word
    part: content_type
    words:
    - application/json
  - type: status
    status:
    - 200
