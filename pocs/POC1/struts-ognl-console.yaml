id: ca038bfb-7594-4866-9cfb-d9ad0c44f710
info:
  name: Apache Struts - OGNL Console
  author: DhiyaneshDK
  severity: unknown
  description: 'This development console allows the evaluation of OGNL expressions
    that could lead to Remote Command Execution

    '
  remediation: Restrict access to the struts console on the production server
  reference:
  - https://github.com/PortSwigger/j2ee-scan/blob/master/src/main/java/burp/j2ee/issues/impl/ApacheStrutsWebConsole.java
  metadata:
    verified: true
    max-request: 1
    shodan-query: html:"Struts Problem Report"
  tags: apache,struts,ognl,panel,misconfig
  poc_id: ca038bfb-7594-4866-9cfb-d9ad0c44f710
  old_id: struts-ognl-console
http:
- method: GET
  path:
  - '{{BaseURL}}/struts/webconsole.html?debug=console'
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - Welcome to the OGNL console!
  - type: status
    status:
    - 200
