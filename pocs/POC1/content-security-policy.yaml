id: 80f09348-93df-4a3c-8623-212958d74371
info:
  name: Missing HTTP Header - Content-Security-Policy
  author: nvsecurity
  severity: low
  reference:
  - https://cheatsheetseries.owasp.org/cheatsheets/HTTP_Headers_Cheat_Sheet.html#content-security-policy-csp
  - https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Security-Policy
  description: 'The website is missing the `Content-Security-Policy` security header.
    The lack of this header *could* allow an attacker to inject arbitrary JavaScript
    into the page.

    '
  metadata:
    max-request: 1
  tags: misconfig,headers
  poc_id: 80f09348-93df-4a3c-8623-212958d74371
  old_id: http-headers-content-security-policy
http:
- method: GET
  path:
  - '{{BaseURL}}'
  host-redirects: true
  max-redirects: 3
  matchers-condition: or
  matchers:
  - type: dsl
    name: content-security-policy
    dsl:
    - '!regex(''(?i)content-security-policy'', header)'
    - status_code != 301 && status_code != 302 && status_code != 404
    condition: and
