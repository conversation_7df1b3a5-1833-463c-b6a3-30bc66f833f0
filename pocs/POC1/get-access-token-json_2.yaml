id: b7baf361-43d6-4f65-b921-00dbc28de68c
info:
  name: Get Access Token Json
  author: DhiyaneshDK
  severity: low
  description: Internal file is exposed in Constant Contact Forms wordpress plugin.
  metadata:
    verified: true
    max-request: 2
    google-query: intitle:"index of" "get_access_token.json"
  tags: exposure,files
  poc_id: b7baf361-43d6-4f65-b921-00dbc28de68c
  old_id: get-access-token-json
http:
- method: GET
  path:
  - '{{BaseURL}}/wp-content/plugins/constant-contact-forms/vendor/constantcontact/constantcontact/test/Json/Auth/get_access_token.json'
  - '{{BaseURL}}/wp-content/plugins/constant-contact-api-old/vendor/constantcontact/constantcontact/test/Json/Auth/get_access_token.json'
  stop-at-first-match: true
  matchers-condition: and
  matchers:
  - type: word
    words:
    - '"access_token":'
    - '"token_type":'
    condition: and
  - type: status
    status:
    - 200
