id: 4a8058aa-2be2-4305-93fa-b349b53825b2
info:
  name: Django Debug Method Enabled
  author: dhiyaneshDK & hackergautam
  severity: medium
  tags: django,debug
  poc_id: 4a8058aa-2be2-4305-93fa-b349b53825b2
  old_id: django-debug
requests:
- method: GET
  path:
  - '{{BaseURL}}/NON_EXISTING_PATH/'
  matchers-condition: and
  matchers:
  - type: word
    words:
    - URLconf defined
    - Page not found
    - <PERSON>jan<PERSON> tried these URL patterns, in this order
    condition: and
  - type: status
    status:
    - 404
