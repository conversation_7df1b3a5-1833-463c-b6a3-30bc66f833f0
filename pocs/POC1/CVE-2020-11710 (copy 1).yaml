id: e8705ee6-402a-4032-aae7-2979d6c7e459
info:
  name: Kong API Improper Authorization
  author: medbsq
  severity: critical
  poc_id: e8705ee6-402a-4032-aae7-2979d6c7e459
  old_id: CVE-2020-11710
requests:
- method: GET
  path:
  - '{{BaseURL}}/status'
  headers:
    User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_1) AppleWebKit/537.36
      (KHTML, like Gecko) Chrome/80.0.3984.0 Safari/537.36
  matchers-condition: and
  matchers:
  - type: word
    words:
    - kong_env
    - kong_db_cache_miss
    condition: or
    part: body
  - type: status
    status:
    - 200
