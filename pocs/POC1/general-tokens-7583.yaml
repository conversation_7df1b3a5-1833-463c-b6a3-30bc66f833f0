id: f445ee31-e563-4575-ba52-97a747287568
info:
  name: <PERSON><PERSON>
  author: nadino,geeknik
  severity: unknown
  tags: exposure,token,generic
  poc_id: f445ee31-e563-4575-ba52-97a747287568
  old_id: generic-tokens
requests:
- method: GET
  path:
  - '{{BaseURL}}'
  matchers-condition: and
  matchers:
  - type: dsl
    dsl:
    - regex("TOKEN[\\-|_|A-Z0-9]*(\'|\")?(:|=)(\'|\")?[\\-|_|A-Z0-9]{10}",replace(toupper(body),"",""))
    - regex("API[\\-|_|A-Z0-9]*(\'|\")?(:|=)(\'|\")?[\\-|_|A-Z0-9]{10}",replace(toupper(body),"",""))
    - regex("KEY[\\-|_|A-Z0-9]*(\'|\")?(:|=)(\'|\")?[\\-|_|A-Z0-9]{10}",replace(toupper(body),"",""))
    - regex("SECRET[\\-|_|A-Z0-9]*(\'|\")?(:|=)(\'|\")?[\\-|_|A-Z0-9]{10}",replace(toupper(body),"",""))
    - regex("AUTHORIZATION[\\-|_|A-Z0-9]*(\'|\")?(:|=)(\'|\")?[\\-|_|A-Z0-9]{10}",replace(toupper(body),"",""))
    - regex("PASSWORD[\\-|_|A-Z0-9]*(\'|\")?(:|=)(\'|\")?[\\-|_|A-Z0-9]{10}",replace(toupper(body),"",""))
  - type: regex
    part: body
    regex:
    - (?i)key(sinternal|up|down|press|boardnavigation|words?|board|ebrow|board_fill|_retry_interval|_fetched|_expiresat|board_shortcuts|s_close|s_previous|s_next|s_zoom|s_play_pause)
    - (?i)password(protection|lessauth|requirementsashtmllist|emailnotfoundmessage|label|errormessage|message|_checkemail_title|_newfield_retype|_text_new|login_submit|_has_expired_title|_has_expired_text|_error|_hint|_strength)
    - (?i)_(on|fade|init|default|parse|mouse|webgl|calculate|animate|clear|touch)|emit|color|render|stop|start|tostring|mobile|browser|get(highentropy|data|browser|os)
    - (?i)(!native)|(.*keybindings)
    - (?i)(layout|a)key
    - (?i)token_expires_in
    condition: or
    negative: true
  extractors:
  - type: regex
    part: body
    regex:
    - (T|t)(O|o)(K|k)(E|e)(N|n)[\-|_|A-Za-z0-9]*(\''|")?( )*(:|=)+()*(\''|")?[ 0-9A-Za-z\-_]+(\''|")?
    - (A|a)(P|p)(Ii)[\-|_|A-Za-z0-9]*(\''|")?( )*(:|=)( )*(\''|")?[0-9A-Za-z\-_]+(\''|")?
    - (K|k)(E|e)(Y|y)[\-|_|A-Za-z0-9]*(\''|")?( )*(:|=)( )*(\''|")?[0-9A-Za-z\-_]+(\''|")?
    - (S|s)(E|e)(C|c)(R|r)(E|e)(T|t)[\-|_|A-Za-z0-9]*(\''|")?( )*(:|=)()*(\''|")?[
      0-9A-Za-z\-_]+(\''|")?
    - (A|a)(U|u)(T|t)(H|h)(O|o)(R|r)(I|i)(Z|z)(A|a)(T|t)(I|i)(O|o)(N|n)[\-|_|A-Za-z0-9]*(\''|")?()*(:|=)(
      )*(\''|")?[ 0-9A-Za-z\-_]+(\''|")?
    - (P|p)(A|a)(S|s)(S|s)(W|w)(O|o)(R|r)(D|d)[\-|_|A-Za-z0-9]*(\''|")?()*(:|=)( )*(\''|")?[
      0-9A-Za-z\-_]+(\''|")?
