id: 61f87e5b-93b0-4bb0-b760-ee188eb62f99
info:
  name: Moleculer Microservices Project
  author: pussycat0x
  severity: low
  description: 'Moleculer microservice was able to be accessed with no required authentication
    in place.

    '
  metadata:
    verified: 'true'
    shodan-query: title:"Moleculer Microservices Project"
  tags: misconfig,microservice,moleculer,exposure
  poc_id: 61f87e5b-93b0-4bb0-b760-ee188eb62f99
  old_id: moleculer-microservices
http:
- method: GET
  path:
  - '{{BaseURL}}'
  matchers-condition: and
  matchers:
  - type: word
    words:
    - Moleculer Microservices Project
    - Service/Action name
    condition: and
  - type: word
    part: header
    words:
    - text/html
  - type: status
    status:
    - 200
