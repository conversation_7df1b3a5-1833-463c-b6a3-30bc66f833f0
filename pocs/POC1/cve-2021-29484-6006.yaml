id: 1e1904d4-a6f2-4db7-b0c3-dff50f0dd373
info:
  name: DOM XSS in Ghost CMS
  author: rootxharsh,iamnoooob
  severity: medium
  description: Ghost is a Node.js CMS. An unused endpoint added during the development
    of 4.0.0 has left sites vulnerable to untrusted users gaining access to Ghost
    Admin. Attackers can gain access by getting logged in users to click a link containing
    malicious code. Users do not need to enter credentials and may not know they've
    visited a malicious site.
  reference:
  - https://github.com/TryGhost/Ghost/security/advisories/GHSA-9fgx-q25h-jxrg
  - https://nvd.nist.gov/vuln/detail/CVE-2021-29484
  - https://www.npmjs.com/package/ghost
  - https://forum.ghost.org/t/critical-security-update-available-for-ghost-4-x/22290
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
    cvss-score: 6.1
    cve-id: CVE-2021-29484
    cwe-id: CWE-79
  tags: cve,cve2021,xss,ghost
  poc_id: 1e1904d4-a6f2-4db7-b0c3-dff50f0dd373
  old_id: CVE-2021-29484
requests:
- method: GET
  path:
  - '{{BaseURL}}/ghost/preview'
  matchers-condition: and
  matchers:
  - type: word
    words:
    - XMLHttpRequest.prototype.open
    part: body
  - type: word
    words:
    - text/html
    part: header
  - type: status
    status:
    - 200
