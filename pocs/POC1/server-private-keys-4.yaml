id: 6f13cd0c-8a88-49a5-a784-68947db6556f
info:
  name: Detect Private SSH, TLS, and JWT Keys
  author: geeknik
  severity: high
  tags: config,exposure
  poc_id: 6f13cd0c-8a88-49a5-a784-68947db6556f
  old_id: server-private-keys4
requests:
- raw:
  - 'GET /private-key HTTP/1.1

    Host: {{Hostname}}

    User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:55.0) Gecko/20100101
    Firefox/55

    '
  matchers-condition: and
  matchers:
  - type: word
    words:
    - BEGIN OPENSSH PRIVATE KEY
    - BEGIN PRIVATE KEY
    - BEGIN RSA PRIVATE KEY
    - BEGIN DSA PRIVATE KEY
    - BEGIN EC PRIVATE KEY
    - BEGIN PGP PRIVATE KEY BLOCK
    condition: or
  - type: status
    status:
    - 200
