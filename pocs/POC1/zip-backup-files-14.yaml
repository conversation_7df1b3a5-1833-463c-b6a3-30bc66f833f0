id: 87e3ff68-2e69-41d7-8e7a-6a7c9b514e96
info:
  name: Compressed Web File
  author: <PERSON><PERSON><PERSON>,dwisiswant0
  severity: medium
  tags: exposure,backup
  poc_id: 87e3ff68-2e69-41d7-8e7a-6a7c9b514e96
  old_id: zip-backup-files14
requests:
- raw:
  - 'GET /{{Hostname}}.sql.bz2 HTTP/1.1

    Host: {{Hostname}}

    User-Agent: Mozilla/5.0 (Windows NT 10.0; rv:68.0) Gecko/20100101 Firefox/68.0

    Accept-Encoding: gzip, deflate

    Accept: */*

    '
  max-size: 500
  matchers-condition: and
  matchers:
  - type: binary
    binary:
    - 377ABCAF271C
    - '314159265359'
    - 53514c69746520666f726d6174203300
    - 1f8b
    - 526172211A0700
    - 526172211A070100
    - FD377A585A0000
    - 1F9D
    - 1FA0
    - 4C5A4950
    - 504B0304
    condition: or
    part: body
  - type: regex
    regex:
    - application/[-\w.]+
    part: header
  - type: status
    status:
    - 200
