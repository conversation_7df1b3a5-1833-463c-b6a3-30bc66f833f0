id: 0e0b49ee-21a6-4b9e-b5cc-7268ea111964
info:
  name: Apache Struts - ShowCase Application Exposure
  author: DhiyaneshDK
  severity: low
  reference:
  - https://github.com/PortSwigger/j2ee-scan/blob/master/src/main/java/burp/j2ee/issues/impl/ApacheStrutsWebConsole.java
  metadata:
    verified: 'true'
    shodan-query: title:"Struts2 Showcase"
  tags: apache,struts,showcase,misconfig,exposure
  poc_id: 0e0b49ee-21a6-4b9e-b5cc-7268ea111964
  old_id: apache-struts-showcase
requests:
- method: GET
  path:
  - '{{BaseURL}}'
  - '{{BaseURL}}/struts2-showcase/showcase.action'
  stop-at-first-match: true
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - <title>Struts2 Showcase
  - type: status
    status:
    - 200
