id: 9f26188c-fcab-49e0-ab8d-6079e9406285
info:
  name: VMware vSphere Client (HTML5) - Remote Code Execution
  author: dwisiswant0
  severity: critical
  description: VMware vCenter vSphere Client (HTML5) contains a remote code execution
    vulnerability in a vCenter Server plugin. A malicious actor with network access
    to port 443 may exploit this issue to execute commands with unrestricted privileges
    on the underlying operating system that hosts vCenter Server. This affects VMware
    vCenter Server (7.x before 7.0 U1c, 6.7 before 6.7 U3l and 6.5 before 6.5 U3n)
    and VMware Cloud Foundation (4.x before 4.2 and 3.x before ********).
  reference:
  - https://swarm.ptsecurity.com/unauth-rce-vmware/
  - https://nvd.nist.gov/vuln/detail/CVE-2021-21972
  - https://www.vmware.com/security/advisories/VMSA-2021-0002.html
  - http://packetstormsecurity.com/files/161590/VMware-vCenter-Server-7.0-Arbitrary-File-Upload.html
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2021-21972
    cwe-id: CWE-269
  tags: cve,cve2021,vmware,rce,vcenter,kev
  poc_id: 9f26188c-fcab-49e0-ab8d-6079e9406285
  old_id: CVE-2021-21972
requests:
- method: GET
  path:
  - '{{BaseURL}}/ui/vropspluginui/rest/services/getstatus'
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 200
  - type: word
    words:
    - VSPHERE-UI-JSESSIONID
    part: header
    condition: and
  - type: regex
    regex:
    - (Install|Config) Final Progress
    part: body
