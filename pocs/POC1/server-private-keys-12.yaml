id: 32de3a23-1f42-4eb8-9a01-4955d5b93f5e
info:
  name: Detect Private SSH, TLS, and JWT Keys
  author: geeknik
  severity: high
  tags: config,exposure
  poc_id: 32de3a23-1f42-4eb8-9a01-4955d5b93f5e
  old_id: server-private-keys12
requests:
- raw:
  - 'GET /id_dsa HTTP/1.1

    Host: {{Hostname}}

    User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:55.0) Gecko/20100101
    Firefox/55

    '
  matchers-condition: and
  matchers:
  - type: word
    words:
    - BEGIN OPENSSH PRIVATE KEY
    - BEGIN PRIVATE KEY
    - BEGIN RSA PRIVATE KEY
    - BEGIN DSA PRIVATE KEY
    - BEGIN EC PRIVATE KEY
    - BEGIN PGP PRIVATE KEY BLOCK
    condition: or
  - type: status
    status:
    - 200
