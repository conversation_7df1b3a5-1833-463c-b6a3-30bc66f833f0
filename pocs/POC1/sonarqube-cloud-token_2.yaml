id: eca5bd46-937e-495d-a452-b59f57035e94
info:
  name: SonarQube Cloud Token Disclosure
  author: DhiyaneshDk
  severity: high
  reference:
  - https://sonarcloud.io/web_api/api/authentication
  - https://docs.sonarsource.com/sonarqube/latest/user-guide/user-account/generating-and-using-tokens/#types-of-tokens
  metadata:
    max-request: 1
    verified: true
  tags: exposure,token,sonarqube
  poc_id: eca5bd46-937e-495d-a452-b59f57035e94
  old_id: sonarqube-cloud-token
flow: http(1) && http(2)
http:
- method: GET
  path:
  - '{{BaseURL}}'
  matchers:
  - type: regex
    part: body
    name: token
    regex:
    - \b[a-fA-F\d]{40}\b
    internal: true
- raw:
  - '@Host: https://sonarcloud.io:443

    GET /api/authentication/validate HTTP/1.1

    Host: sonarcloud.io

    Authorization: Basic {{base64(token + '':'')}}

    '
  disable-path-automerge: true
  matchers:
  - type: dsl
    dsl:
    - status_code == 200
    - contains(body, 'valid') && contains(body, 'true')
    - contains(content_type, "application/json")
    condition: and
  extractors:
  - type: dsl
    dsl:
    - token
