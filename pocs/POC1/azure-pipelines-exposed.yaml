id: 11dbb259-1df4-4539-9c1c-84e3e8d87cf5
info:
  name: Azure Pipelines Configuration File Disclosure
  author: DhiyaneshDk
  severity: medium
  description: Azure Pipelines internal critical file is disclosed.
  metadata:
    verified: true
    max-request: 2
    shodan-query: html:"azure-pipelines.yml"
  tags: config,exposure,azure,microsoft,cloud,devops,files
  poc_id: 11dbb259-1df4-4539-9c1c-84e3e8d87cf5
  old_id: azure-pipelines-exposed
http:
- method: GET
  path:
  - '{{BaseURL}}/.azure-pipelines.yml'
  - '{{BaseURL}}/azure-pipelines.yml'
  stop-at-first-match: true
  matchers-condition: and
  matchers:
  - type: word
    words:
    - 'trigger:'
    - 'pool:'
    - 'variables:'
    condition: and
  - type: status
    status:
    - 200
