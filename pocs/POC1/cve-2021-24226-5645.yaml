id: 3cfba424-c263-47a5-b3e4-0e20ad548c5c
info:
  name: AccessAlly < 3.5.7 - $_SERVER Superglobal Leakage
  author: dhiyaneshDK
  severity: high
  description: In the AccessAlly WordPress plugin before 3.5.7, the file \"resource/frontend/product/product-shortcode.php\"
    responsible for the [accessally_order_form] shortcode is dumping serialize($_SERVER),
    which contains all environment variables. The leakage occurs on all public facing
    pages containing the [accessally_order_form] shortcode, no login or administrator
    role is required.
  reference:
  - https://wpscan.com/vulnerability/8e3e89fd-e380-4108-be23-00e87fbaad16
  - https://nvd.nist.gov/vuln/detail/CVE-2021-24226
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
    cvss-score: 7.5
    cve-id: CVE-2021-24226
    cwe-id: CWE-200
  tags: wordpress,cve,cve2021,wp-plugin
  poc_id: 3cfba424-c263-47a5-b3e4-0e20ad548c5c
  old_id: CVE-2021-24226
requests:
- method: GET
  path:
  - '{{BaseURL}}'
  matchers-condition: and
  matchers:
  - type: word
    words:
    - <div id="accessally-testing-data"
    condition: and
    part: body
  - type: status
    status:
    - 200
