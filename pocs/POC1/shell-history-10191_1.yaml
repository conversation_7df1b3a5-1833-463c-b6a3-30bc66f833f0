id: be61e344-9e5c-4783-bccd-4ba57a322fc8
info:
  name: Shell History
  author: pentest_swissky & geeknik
  severity: low
  description: Discover history for bash, ksh, sh, and zsh
  tags: config
  poc_id: be61e344-9e5c-4783-bccd-4ba57a322fc8
  old_id: shell-history
requests:
- method: GET
  redirects: true
  max-redirects: 1
  path:
  - '{{BaseURL}}/.bash_history'
  - '{{BaseURL}}/.ksh_history'
  - '{{BaseURL}}/.sh_history'
  - '{{BaseURL}}/.zsh_history'
  matchers-condition: and
  matchers:
  - type: word
    words:
    - 'chmod '
    - exit
    - 'kill '
    - 'nano '
    - 'vim '
    - 'pico '
    - 'sudo '
    - 'rm '
    - 'cd '
    - 'ps aux '
    condition: or
  - type: status
    status:
    - 200
  - type: word
    words:
    - <html
    part: body
    negative: true
