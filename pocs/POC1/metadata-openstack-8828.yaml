id: 74e6b055-874b-471e-b166-36c3db7dc614
info:
  name: Openstack Metadata Service Check
  author: sullo
  severity: critical
  description: The Openstack host is configured as a proxy which allows access to
    the instance metadata service. This could allow significant access to the host/infrastructure.
  remediation: Disable the proxy or restrict configuration to only allow access to
    approved hosts/ports. Upgrade to IMDSv2 if possible.
  reference:
  - https://docs.openstack.org/nova/latest/admin/metadata-service.html
  - https://blog.projectdiscovery.io/abusing-reverse-proxies-metadata/
  - https://www.mcafee.com/blogs/enterprise/cloud-security/how-an-attacker-could-use-instance-metadata-to-breach-your-app-in-aws/
  tags: exposure,config,openstack,proxy,misconfig,metadata
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:L/A:N
    cvss-score: 9.3
    cwe-id: CWE-441
  poc_id: 74e6b055-874b-471e-b166-36c3db7dc614
  old_id: metadata-service-openstack
requests:
- raw:
  - 'GET http://{{hostval}}/openstack/latest HTTP/1.1

    Host: {{hostval}}

    '
  payloads:
    hostval:
    - aws.interact.sh
    - ***************
  unsafe: true
  matchers:
  - type: word
    part: body
    words:
    - vendor_data.json
