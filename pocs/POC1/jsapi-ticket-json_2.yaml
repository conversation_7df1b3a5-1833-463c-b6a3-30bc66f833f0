id: 33ba7024-211f-4ef6-b32a-2681c329d71d
info:
  name: JsAPI Ticket Json
  author: DhiyaneshDK
  severity: low
  description: JsAPI Ticket internal file is exposed.
  reference: https://www.exploit-db.com/ghdb/6070
  metadata:
    verified: true
    max-request: 1
    google-query: intitle:"index of" "jsapi_ticket.json"
  tags: exposure,jsapi,files
  poc_id: 33ba7024-211f-4ef6-b32a-2681c329d71d
  old_id: jsapi-ticket-json
http:
- method: GET
  path:
  - '{{BaseURL}}/jsapi_ticket.json'
  matchers-condition: and
  matchers:
  - type: word
    words:
    - '"expire_time":'
    - '"jsapi_ticket":'
    condition: and
  - type: status
    status:
    - 200
