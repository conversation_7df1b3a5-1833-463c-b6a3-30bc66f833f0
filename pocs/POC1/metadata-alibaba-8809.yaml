id: 7fd33b48-3bc6-4db2-afce-0758a7e09c5e
info:
  name: Alibaba Metadata Service Check
  author: sullo
  severity: critical
  description: The Alibaba host is configured as a proxy which allows access to the
    metadata service. This could allow significant access to the host/infrastructure.
  remediation: Disable the proxy or restrict configuraiton to only allow access to
    approved hosts/ports. Upgrade to IMDSv2 if possible.
  reference:
  - https://www.alibabacloud.com/help/doc-detail/108460.htm
  - https://blog.projectdiscovery.io/abusing-reverse-proxies-metadata/
  - https://www.mcafee.com/blogs/enterprise/cloud-security/how-an-attacker-could-use-instance-metadata-to-breach-your-app-in-aws/
  tags: exposure,config,alibaba,proxy,misconfig,metadata
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:L/A:N
    cvss-score: 9.3
    cwe-id: CWE-441
  poc_id: 7fd33b48-3bc6-4db2-afce-0758a7e09c5e
  old_id: metadata-service-alibaba
requests:
- raw:
  - 'GET http://{{hostval}}/dynamic/instance-identity/document HTTP/1.1

    Host: {{hostval}}


    '
  payloads:
    hostval:
    - alibaba.interact.sh
    - ***************
  unsafe: true
  matchers:
  - type: word
    part: body
    words:
    - zone-id
