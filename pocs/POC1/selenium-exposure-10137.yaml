id: ff3e927d-e1b5-4099-93e7-050f151cd892
info:
  name: Selenium - Node Exposure
  author: w0Tx
  severity: high
  description: 'Selenium was shown to have an exposed node. If a Selenium node is
    exposed without any form of authentication, remote command execution could be
    possible if chromium is configured. By default the port is 4444, still, most of
    the internet facing are done through reverse proxies.

    '
  reference:
  - https://nutcrackerssecurity.github.io/selenium.html
  - https://labs.detectify.com/2017/10/06/guest-blog-dont-leave-your-grid-wide-open/
  metadata:
    verified: true
    shodan-query: /wd/hub
  tags: selenium,misconfiguration,rce,chromium
  poc_id: ff3e927d-e1b5-4099-93e7-050f151cd892
  old_id: selenium-exposure
requests:
- method: GET
  path:
  - '{{BaseURL}}/wd/hub'
  redirects: true
  max-redirects: 2
  matchers-condition: and
  matchers:
  - type: word
    words:
    - WebDriverRequest
    - <title>WebDriver Hub</title>
    condition: or
  - type: status
    status:
    - 200
