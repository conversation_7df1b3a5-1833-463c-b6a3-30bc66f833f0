id: 5c72e545-d113-4b96-bde1-b1c624cb0dc1
info:
  name: Publicly Accessible Phpmyadmin Setup
  author: she<PERSON><PERSON><PERSON><PERSON>,thevillagehacker,Kr1shna4garwal
  severity: medium
  tags: phpmyadmin,misconfig
  poc_id: 5c72e545-d113-4b96-bde1-b1c624cb0dc1
  old_id: phpmyadmin-setup
requests:
- method: GET
  path:
  - '{{BaseURL}}/phpmyadmin/scripts/setup.php'
  - '{{BaseURL}}/_phpmyadmin/scripts/setup.php'
  - '{{BaseURL}}/forum/phpmyadmin/scripts/setup.php'
  - '{{BaseURL}}/php/phpmyadmin/scripts/setup.php'
  - '{{BaseURL}}/typo3/phpmyadmin/scripts/setup.php'
  - '{{BaseURL}}/web/phpmyadmin/scripts/setup.php'
  - '{{BaseURL}}/xampp/phpmyadmin/scripts/setup.php'
  - '{{BaseURL}}/sysadmin/phpMyAdmin/scripts/setup.php'
  - '{{BaseURL}}/phpmyadmin/setup/index.php'
  - '{{BaseURL}}/pma/setup/index.php'
  - '{{BaseURL}}/phpmyadmin/setup/'
  stop-at-first-match: true
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - You want to configure phpMyAdmin using web interface
    - <title>phpMyAdmin setup</title>
    condition: or
  - type: status
    status:
    - 200
