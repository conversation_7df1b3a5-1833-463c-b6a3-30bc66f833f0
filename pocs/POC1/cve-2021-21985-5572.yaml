id: 5be940b6-f833-46af-bb5a-5fb308a9563e
info:
  name: VMware vSphere Client (HTML5) - Remote Code Execution
  author: D0rkerDevil
  severity: critical
  description: 'The vSphere Client (HTML5) contains a remote code execution vulnerability
    due to lack of input validation in the Virtual SAN Health Check plug-in which
    is enabled by default in vCenter Server. A malicious actor with network access
    to port 443 may exploit this issue to execute commands with unrestricted privileges
    on the underlying operating system that hosts vCenter Server.

    '
  reference:
  - https://www.vmware.com/security/advisories/VMSA-2021-0010.html
  - https://github.com/alt3kx/CVE-2021-21985_PoC
  - https://nvd.nist.gov/vuln/detail/CVE-2021-21985
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2021-21985
    cwe-id: CWE-20
  tags: cve,cve2021,rce,vsphere,vmware,kev
  poc_id: 5be940b6-f833-46af-bb5a-5fb308a9563e
  old_id: CVE-2021-21985
requests:
- raw:
  - 'POST /ui/h5-vsan/rest/proxy/service/com.vmware.vsan.client.services.capability.VsanCapabilityProvider/getClusterCapabilityData
    HTTP/1.1

    Host: {{Hostname}}

    Accept: */*

    Content-Type: application/json


    {"methodInput":[{"type":"ClusterComputeResource","value": null,"serverGuid": null}]}

    '
  matchers:
  - type: word
    words:
    - '{"result":{"isDisconnected":'
    part: body
